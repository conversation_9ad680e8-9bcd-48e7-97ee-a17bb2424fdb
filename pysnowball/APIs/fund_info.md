```json
{
    "data": {
        "fd_code": "008975",
        "fd_type": "5",
        "fd_name": "富国中证消费50ETF联接A",
        "fd_full_name": "富国中证消费50ETF联接A",
        "found_date": "2020-03-18",
        "fd_status": "1",
        "declare_status": "1",
        "subscribe_status": "0",
        "withdraw_status": "1",
        "auto_invest_status": "1",
        "totshare": "5亿",
        "keeper_name": "富国基金管理有限公司",
        "manager_name": "王乐乐",
        "trup_name": "中信证券股份有限公司",
        "sale_status": "1",
        "risk_level": "3",
        "ipo_start_date": 1582473600000,
        "ipo_end_date": 1584028800000,
        "fund_derived": {
            "end_date": "2022-08-15",
            "unit_nav": "1.3635",
            "nav_grtd": "-0.8003",
            "nav_grl1m": "-3.7348206721",
            "nav_grl3m": "4.0998625744",
            "nav_grl6m": "-9.552238806",
            "nav_grlty": "-13.8878363016",
            "nav_grl1y": "-11.4437877509",
            "nav_grbase": "36.35",
            "srank_l1m": "1159/1242",
            "srank_l3m": "864/1203",
            "srank_l6m": "853/1143",
            "srank_lty": "779/1267",
            "srank_l1y": "454/907",
            "nav_growth": "-0.0110",
            "annual_performance_list": [
                {
                    "period": "成立以来",
                    "nav": "36.35",
                    "rank": "246/1009"
                },
                {
                    "period": "今年以来",
                    "nav": "-13.89",
                    "rank": "639/1009"
                },
                {
                    "period": "2021",
                    "nav": "-11.91",
                    "rank": "586/620"
                }
            ],
            "yield_history": [
                {
                    "yield": "4.10",
                    "name": "近3个月"
                },
                {
                    "yield": "-9.55",
                    "name": "近6个月"
                },
                {
                    "yield": "-11.44",
                    "name": "近一年"
                },
                {
                    "yield": "-6.75",
                    "name": "近两年"
                },
                {
                    "name": "近三年"
                },
                {
                    "name": "近五年"
                },
                {
                    "yield": "36.35",
                    "name": "成立以来"
                }
            ]
        },
        "fund_rates": {
            "subscribe_rate": "1.0",
            "declare_rate": "1.2",
            "discount": "0.1",
            "subscribe_discount": "0.1",
            "declare_discount": "0.1"
        },
        "op_fund": {
            "fund_tags": [
                {
                    "category": "1",
                    "name": "指数型"
                },
                {
                    "category": "9",
                    "name": "中风险"
                }
            ]
        },
        "yield": "36.35",
        "yield_name": "成立以来收益率",
        "growth_day": "360",
        "sales": "",
        "type_desc": "指数型",
        "rating_desc": "暂无评级",
        "follower_count": 11687,
        "status_count": 765,
        "stock_position_names": "贵州茅台,中国中免,伊利股份,...",
        "agent_sell": true,
        "trade_reason": {
            "show_button": false,
            "withdraw_display": true
        },
        "growth_default_period": "ty",
        "fir_header_base_data": [
            {
                "data_name": "今年以来",
                "data_value_str": "-13.89%",
                "data_value_number": -13.8878363016,
                "data_have_colour": true
            },
            {
                "data_name": "累计收益",
                "data_value_str": "+36.35%",
                "data_value_number": 36.35,
                "data_have_colour": true
            },
            {
                "data_name": "日涨跌",
                "data_value_str": "-0.80%",
                "data_value_number": -0.8003,
                "data_have_colour": true
            }
        ],
        "sec_header_base_data": [
            {
                "data_name": "成立以来年化",
                "data_value_str": "+13.72%",
                "data_value_number": 13.7237784493,
                "data_have_colour": true
            },
            {
                "data_name": "成立时长",
                "data_value_str": "2年151天",
                "data_have_colour": false
            },
            {
                "data_name": "最新净值",
                "data_value_str": "1.3635",
                "data_value_number": 1.3635,
                "data_have_colour": false,
                "data_extend": "08-15"
            },
            {
                "data_name": "最大回撤",
                "data_value_str": "41.90%",
                "data_value_number": 0.419,
                "data_have_colour": false
            },
            {
                "data_name": "基金经理",
                "data_value_str": "王乐乐",
                "data_have_colour": false
            },
            {
                "data_name": "基金规模",
                "data_value_str": "5亿",
                "data_have_colour": false
            }
        ],
        "base_data_tip": [
            {
                "title": "成立以来年化",
                "content": "即成立以来年化收益率（复权），是基金成立以来的累计收益率按复利计算的年化值。若基金成立时间不足半年，不具备参考价值，不显示该数据。"
            },
            {
                "title": "最大回撤",
                "content": "基金成立以来，从任一历史时点往后推，净值走到最低点时的收益率回撤幅度的最大值。最大回撤用来描述买入产品后可能出现的最糟糕情况。通常用来衡量该基金的抗风险能力。"
            }
        ],
        "nav_tab_list": [
            {
                "nav_tab_name": "今年以来",
                "nav_tab_value": "ty",
                "nav_growth": "-13.8878363016"
            },
            {
                "nav_tab_name": "近1月",
                "nav_tab_value": "1m",
                "nav_growth": "-3.7348206721"
            },
            {
                "nav_tab_name": "近3月",
                "nav_tab_value": "3m",
                "nav_growth": "4.0998625744"
            },
            {
                "nav_tab_name": "近1年",
                "nav_tab_value": "1y",
                "nav_growth": "-11.4437877509"
            },
            {
                "nav_tab_name": "近6月",
                "nav_tab_value": "6m",
                "nav_growth": "-9.552238806"
            },
            {
                "nav_tab_name": "近3年",
                "nav_tab_value": "3y"
            },
            {
                "nav_tab_name": "近5年",
                "nav_tab_value": "5y"
            },
            {
                "nav_tab_name": "成立来",
                "nav_tab_value": "all",
                "nav_growth": "36.35",
                "nav_tab_extent": "2年151天"
            }
        ],
        "display_annual_performance": true,
        "benchmark_index": [
            {
                "symbol": "CSI931139",
                "symbol_name": "中证消费50"
            },
            {
                "symbol": "SH000300",
                "symbol_name": "沪深300指数"
            },
            {
                "symbol": "SH000905",
                "symbol_name": "中证500指数"
            },
            {
                "symbol": "SH000016",
                "symbol_name": "上证50指数"
            },
            {
                "symbol": "SZ399006",
                "symbol_name": "创业板指数"
            },
            {
                "symbol": "SH000001",
                "symbol_name": "上证综指"
            },
            {
                "symbol": "SZ399001",
                "symbol_name": "深证成指"
            },
            {
                "symbol": "CSI930950",
                "symbol_name": "中证偏股基金指数"
            },
            {
                "symbol": "HKHSI",
                "symbol_name": "恒生指数"
            },
            {
                "symbol": "CSIH11001",
                "symbol_name": "中证全债指数"
            }
        ],
        "invest_orientation": "本基金以目标ETF作为其主要投资标的，方便投资者通过本基金投资目标ETF。本基金并不参与目标ETF的管理。在正常市场情况下，本基金力争净值增长率与业绩比较基准之间的日均跟踪偏离度的绝对值不超过0.35%，年跟踪误差不超过4%。如因标的指数编制规则调整或其他因素导致跟踪偏离度和跟踪误差超过正常范围的，基金管理人将采取合理措施避免跟踪误差进一步扩大。\r\n    为实现紧密跟踪标的指数的投资目标，本基金将以不低于基金资产净值90%的资产投资于目标ETF。",
        "invest_target": "本基金通过投资于目标ETF，紧密跟踪标的指数，追求与业绩比较基准相似的回报。",
        "performance_bench_mark": "中证消费50指数收益率×95%+银行活期存款利率（税后）×5%",
        "record_version": "1"
    },
    "result_code": 0
}
```