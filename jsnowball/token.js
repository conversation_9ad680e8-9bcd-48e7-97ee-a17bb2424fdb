// Token管理模块
let xueqiuToken = 'xq_a_token=e007ef456c835e7814569e80b504f59f347460eb;u=1154585495';

// 错误消息常量
const NOTOKEN_ERROR_MSG = "未设置TOKEN";

/**
 * 获取雪球token
 * @returns {string} 雪球token
 * @throws {Error} 如果token未设置则抛出错误
 */
export function getToken() {
    // 优先从环境变量获取
    if (process.env.XUEQIUTOKEN) {
        return process.env.XUEQIUTOKEN;
    }
    
    // 其次从内存变量获取
    if (xueqiuToken) {
        return xueqiuToken;
    }
    
    throw new Error(NOTOKEN_ERROR_MSG);
}

/**
 * 设置雪球token
 * @param {string} token - 雪球token字符串，格式如: "xq_a_token=xxx;u=xxx"
 * @returns {string} 设置的token
 */
export function setToken(token) {
    if (!token || typeof token !== 'string') {
        throw new Error('Token must be a non-empty string');
    }
    
    // 验证token格式（简单验证）
    if (!token.includes('xq_a_token=') || !token.includes('u=')) {
        console.warn('Warning: Token format may be incorrect. Expected format: "xq_a_token=xxx;u=xxx"');
    }
    
    // 同时设置环境变量和内存变量
    process.env.XUEQIUTOKEN = token;
    xueqiuToken = token;
    
    return token;
}

/**
 * 清除token
 */
export function clearToken() {
    delete process.env.XUEQIUTOKEN;
    xueqiuToken = null;
}

/**
 * 检查token是否已设置
 * @returns {boolean} 是否已设置token
 */
export function hasToken() {
    try {
        getToken();
        return true;
    } catch {
        return false;
    }
}

/**
 * 获取token信息（隐藏敏感部分）
 * @returns {Object} token信息
 */
export function getTokenInfo() {
    try {
        const token = getToken();
        const parts = token.split(';');
        const info = {};
        
        parts.forEach(part => {
            const [key, value] = part.split('=');
            if (key && value) {
                // 隐藏敏感信息，只显示前4位和后4位
                if (value.length > 8) {
                    info[key.trim()] = value.substring(0, 4) + '***' + value.substring(value.length - 4);
                } else {
                    info[key.trim()] = '***';
                }
            }
        });
        
        return {
            hasToken: true,
            tokenParts: info,
            length: token.length
        };
    } catch {
        return {
            hasToken: false,
            tokenParts: {},
            length: 0
        };
    }
}
