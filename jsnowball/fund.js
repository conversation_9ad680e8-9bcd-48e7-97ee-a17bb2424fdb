import { fetchDanjuanFund } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 获取基金详细信息
 * @param {string} fundCode - 基金代码
 * @returns {Promise<Object>} 基金详细信息
 */
export async function fundDetail(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_DETAIL, fundCode);
    return await fetchDanjuanFund(url);
}

/**
 * 获取基金基本信息
 * @param {string} fundCode - 基金代码
 * @returns {Promise<Object>} 基金基本信息
 */
export async function fundInfo(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_INFO, fundCode);
    return await fetchDanjuanFund(url);
}

/**
 * 获取基金增长数据
 * @param {string} fundCode - 基金代码
 * @param {string} day - 时间周期，默认'ty'（今年以来）
 * @returns {Promise<Object>} 基金增长数据
 */
export async function fundGrowth(fundCode, day = 'ty') {
    const url = apiRef.formatUrl(apiRef.FUND_GROWTH, fundCode, day);
    return await fetchDanjuanFund(url);
}

/**
 * 获取基金净值历史
 * @param {string} fundCode - 基金代码
 * @param {number} page - 页码，默认1
 * @param {number} size - 每页数据条数，默认10
 * @returns {Promise<Object>} 基金净值历史数据
 */
export async function fundNavHistory(fundCode, page = 1, size = 10) {
    const url = apiRef.formatUrl(apiRef.FUND_NAV_HISTORY, fundCode, page, size);
    return await fetchDanjuanFund(url);
}

/**
 * 获取基金业绩表现
 * @param {string} fundCode - 基金代码
 * @returns {Promise<Object>} 基金业绩表现数据
 */
export async function fundAchievement(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_ACHIEVEMENT, fundCode);
    return await fetchDanjuanFund(url);
}

/**
 * 获取基金资产配置
 * @param {string} fundCode - 基金代码
 * @returns {Promise<Object>} 基金资产配置数据
 */
export async function fundAsset(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_ASSET, fundCode);
    return await fetchDanjuanFund(url);
}

/**
 * 获取基金管理人信息
 * @param {string} fundCode - 基金代码
 * @param {string} postStatus - 状态，默认为空
 * @returns {Promise<Object>} 基金管理人信息
 */
export async function fundManager(fundCode, postStatus = '') {
    const url = apiRef.formatUrl(apiRef.FUND_MANAGER, fundCode, postStatus);
    return await fetchDanjuanFund(url);
}

/**
 * 获取基金交易日期
 * @param {string} fundCode - 基金代码
 * @returns {Promise<Object>} 基金交易日期信息
 */
export async function fundTradeDate(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_TRADE_DATE, fundCode);
    return await fetchDanjuanFund(url);
}

/**
 * 获取基金衍生数据
 * @param {string} fundCode - 基金代码
 * @returns {Promise<Object>} 基金衍生数据
 */
export async function fundDerived(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_DERIVED, fundCode);
    return await fetchDanjuanFund(url);
}

/**
 * 获取基金完整信息
 * @param {string} fundCode - 基金代码
 * @param {Object} options - 可选参数
 * @param {number} options.navPage - 净值历史页码
 * @param {number} options.navSize - 净值历史每页条数
 * @param {string} options.growthDay - 增长数据时间周期
 * @returns {Promise<Object>} 基金完整信息
 */
export async function fullFundInfo(fundCode, options = {}) {
    const { navPage = 1, navSize = 10, growthDay = 'ty' } = options;
    
    const [
        detailData,
        infoData,
        growthData,
        navHistoryData,
        achievementData,
        assetData,
        managerData,
        tradeDateData,
        derivedData
    ] = await Promise.all([
        fundDetail(fundCode),
        fundInfo(fundCode),
        fundGrowth(fundCode, growthDay),
        fundNavHistory(fundCode, navPage, navSize),
        fundAchievement(fundCode),
        fundAsset(fundCode),
        fundManager(fundCode),
        fundTradeDate(fundCode),
        fundDerived(fundCode)
    ]);
    
    return {
        detail: detailData,
        info: infoData,
        growth: growthData,
        navHistory: navHistoryData,
        achievement: achievementData,
        asset: assetData,
        manager: managerData,
        tradeDate: tradeDateData,
        derived: derivedData
    };
}

/**
 * 获取基金基本信息概览
 * @param {string} fundCode - 基金代码
 * @returns {Promise<Object>} 基金基本信息概览
 */
export async function fundOverview(fundCode) {
    const [detailData, infoData, growthData] = await Promise.all([
        fundDetail(fundCode),
        fundInfo(fundCode),
        fundGrowth(fundCode)
    ]);
    
    return {
        detail: detailData,
        info: infoData,
        growth: growthData
    };
}

/**
 * 获取多个基金的基本信息
 * @param {Array<string>} fundCodes - 基金代码数组
 * @returns {Promise<Array<Object>>} 多个基金的基本信息
 */
export async function batchFundInfo(fundCodes) {
    if (!Array.isArray(fundCodes)) {
        throw new Error('fundCodes must be an array');
    }
    
    const promises = fundCodes.map(code => fundOverview(code));
    return await Promise.all(promises);
}
