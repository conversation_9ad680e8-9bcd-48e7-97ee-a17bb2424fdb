import { fetch } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 获取组合净值历史数据
 * @param {string} symbol - 组合代码
 * @returns {Promise<Object>} 组合净值历史数据
 */
export async function navDaily(symbol) {
    const url = apiRef.NAV_DAILY + symbol;
    return await fetch(url, 'xueqiu.com');
}

/**
 * 获取组合调仓历史
 * @param {string} symbol - 组合代码
 * @param {number} count - 返回数据条数，默认20
 * @param {number} page - 页码，默认1
 * @returns {Promise<Object>} 组合调仓历史数据
 */
export async function rebalancingHistory(symbol, count = 20, page = 1) {
    let url = apiRef.REBALANCING_HISTORY + symbol;
    url += '&count=' + count;
    url += '&page=' + page;
    
    return await fetch(url, 'xueqiu.com');
}

/**
 * 获取组合当前持仓
 * @param {string} symbol - 组合代码
 * @returns {Promise<Object>} 组合当前持仓数据
 */
export async function rebalancingCurrent(symbol) {
    const url = apiRef.REBALANCING_CURRENT + symbol;
    return await fetch(url, 'xueqiu.com');
}

/**
 * 获取组合实时净值
 * @param {string} symbol - 组合代码
 * @returns {Promise<Object>} 组合实时净值数据
 */
export async function quoteCurrent(symbol) {
    const url = apiRef.QUOTE_CURRENT + symbol;
    return await fetch(url, 'xueqiu.com');
}

/**
 * 获取组合完整信息
 * @param {string} symbol - 组合代码
 * @param {Object} options - 可选参数
 * @param {number} options.historyCount - 调仓历史数据条数
 * @param {number} options.historyPage - 调仓历史页码
 * @returns {Promise<Object>} 组合完整信息
 */
export async function fullCubeInfo(symbol, options = {}) {
    const { historyCount = 20, historyPage = 1 } = options;
    
    const [navData, historyData, currentData, quoteData] = await Promise.all([
        navDaily(symbol),
        rebalancingHistory(symbol, historyCount, historyPage),
        rebalancingCurrent(symbol),
        quoteCurrent(symbol)
    ]);
    
    return {
        nav: navData,
        history: historyData,
        current: currentData,
        quote: quoteData
    };
}

/**
 * 获取多个组合的实时净值
 * @param {Array<string>} symbols - 组合代码数组
 * @returns {Promise<Array<Object>>} 多个组合的实时净值数据
 */
export async function batchQuoteCurrent(symbols) {
    if (!Array.isArray(symbols)) {
        throw new Error('symbols must be an array');
    }
    
    const promises = symbols.map(symbol => quoteCurrent(symbol));
    return await Promise.all(promises);
}

/**
 * 获取组合基本信息和表现
 * @param {string} symbol - 组合代码
 * @returns {Promise<Object>} 组合基本信息和表现数据
 */
export async function cubeOverview(symbol) {
    const [quoteData, navData] = await Promise.all([
        quoteCurrent(symbol),
        navDaily(symbol)
    ]);
    
    return {
        quote: quoteData,
        nav: navData
    };
}
