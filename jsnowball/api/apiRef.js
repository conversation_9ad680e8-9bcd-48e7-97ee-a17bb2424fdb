// Finance APIs
export const FINANCE_CASH_FLOW_URL = "https://stock.xueqiu.com/v5/stock/finance/cn/cash_flow.json?symbol=";
export const FINANCE_INDICATOR_URL = "https://stock.xueqiu.com/v5/stock/finance/cn/indicator.json?symbol=";
export const FINANCE_BALANCE_URL = "https://stock.xueqiu.com/v5/stock/finance/cn/balance.json?symbol=";
export const FINANCE_INCOME_URL = "https://stock.xueqiu.com/v5/stock/finance/cn/income.json?symbol=";
export const FINANCE_BUSINESS_URL = "https://stock.xueqiu.com/v5/stock/finance/cn/business.json?symbol=";

// Report APIs
export const REPORT_LATEST_URL = "https://stock.xueqiu.com/stock/report/latest.json?symbol=";
export const REPORT_EARNINGFORECAST_URL = "https://stock.xueqiu.com/stock/report/earningforecast.json?symbol=";

// Capital APIs
export const CAPITAL_MARGIN_URL = "https://stock.xueqiu.com/v5/stock/capital/margin.json?symbol=";
export const CAPITAL_BLOCKTRANS_URL = "https://stock.xueqiu.com/v5/stock/capital/blocktrans.json?symbol=";
export const CAPITAL_ASSORT_URL = "https://stock.xueqiu.com/v5/stock/capital/assort.json?symbol=";
export const CAPITAL_HISTORY_URL = "https://stock.xueqiu.com/v5/stock/capital/history.json?symbol=";
export const CAPITAL_FLOW_URL = "https://stock.xueqiu.com/v5/stock/capital/flow.json?symbol=";

// F10 APIs
export const F10_SKHOLDERCHG = "https://stock.xueqiu.com/v5/stock/f10/cn/skholderchg.json?symbol=";
export const F10_SKHOLDER = "https://stock.xueqiu.com/v5/stock/f10/cn/skholder.json?symbol=";
export const F10_INDUSTRY = "https://stock.xueqiu.com/v5/stock/f10/cn/industry.json?symbol=";
export const F10_HOLDERS = "https://stock.xueqiu.com/v5/stock/f10/cn/holders.json?symbol=";
export const F10_BONUS = "https://stock.xueqiu.com/v5/stock/f10/cn/bonus.json?symbol=";
export const F10_ORG_HOLDING_CHANGE = "https://stock.xueqiu.com/v5/stock/f10/cn/org_holding_change.json?symbol=";
export const F10_INDUSTRY_COMPARE = "https://stock.xueqiu.com/v5/stock/f10/cn/industry_compare.json?symbol=";
export const F10_BUSINESS_ANALYSIS = "https://stock.xueqiu.com/v5/stock/f10/cn/business_analysis.json?symbol=";
export const F10_SHARESCHG = "https://stock.xueqiu.com/v5/stock/f10/cn/business_analysis.json?symbol=";
export const F10_TOP_HOLDERS = "https://stock.xueqiu.com/v5/stock/f10/cn/top_holders.json?&symbol=";
export const F10_INDICATOR = "https://stock.xueqiu.com/v5/stock/f10/cn/indicator.json?symbol=";

// Real time APIs
export const REALTIME_QUOTE = "https://stock.xueqiu.com/v5/stock/realtime/quotec.json?symbol=";
export const REALTIME_PANKOU = "https://stock.xueqiu.com/v5/stock/realtime/pankou.json?symbol=";
export const REALTIME_QUOTE_DETAIL = "https://stock.xueqiu.com/v5/stock/quote.json?extend=detail&symbol=";

// K-line API
export const KLINE = "https://stock.xueqiu.com/v5/stock/chart/kline.json?symbol={}&begin={}&period={}&type=before&count=-{}&indicator=kline,pe,pb,ps,pcf,market_capital,agt,ggt,balance";

// User APIs
export const WATCH_LIST = "https://stock.xueqiu.com/v5/stock/portfolio/list.json?system=true";
export const WATCH_STOCK = "https://stock.xueqiu.com/v5/stock/portfolio/stock/list.json?size=1000&category=1&pid=";

// Cube APIs
export const NAV_DAILY = "https://xueqiu.com/cubes/nav_daily/all.json?cube_symbol=";
export const REBALANCING_HISTORY = "https://xueqiu.com/cubes/rebalancing/history.json?cube_symbol=";
export const REBALANCING_CURRENT = "https://xueqiu.com/cubes/rebalancing/current.json?cube_symbol=";
export const QUOTE_CURRENT = "https://xueqiu.com/cubes/quote.json?code=";

// EastMoney APIs
export const CONVERTIBLE_BOND = "https://datacenter-web.eastmoney.com/api/data/v1/get?pageSize={}&pageNumber={}&sortColumns=PUBLIC_START_DATE&sortTypes=-1&reportName=RPT_BOND_CB_LIST&columns=ALL&quoteColumns=f2~01~CONVERT_STOCK_CODE~CONVERT_STOCK_PRICE%2Cf235~10~SECURITY_CODE~TRANSFER_PRICE%2Cf236~10~SECURITY_CODE~TRANSFER_VALUE%2Cf2~10~SECURITY_CODE~CURRENT_BOND_PRICE%2Cf237~10~SECURITY_CODE~TRANSFER_PREMIUM_RATIO%2Cf239~10~SECURITY_CODE~RESALE_TRIG_PRICE%2Cf240~10~SECURITY_CODE~REDEEM_TRIG_PRICE%2Cf23~01~CONVERT_STOCK_CODE~PBV_RATIO&source=WEB&client=WEB";

// CSIndex APIs
export const INDEX_BASIC_INFO = "https://www.csindex.com.cn/csindex-home/indexInfo/index-basic-info/{}";
export const INDEX_DETAILS_DATA = "https://www.csindex.com.cn/csindex-home/indexInfo/index-details-data?fileLang=1&indexCode={}";
export const INDEX_WEIGHT_TOP10 = "https://www.csindex.com.cn/csindex-home/index/weight/top10/{}";
export const INDEX_PERF = "https://www.csindex.com.cn/csindex-home/perf/index-perf?indexCode={}&startDate={}&endDate={}";

// HKEX APIs
export const HKEX_CONNECT = "http://www.hkexnews.hk/sdw/search/mutualmarket.aspx?t=";

// Fund APIs (DanJuan)
export const FUND_DETAIL = "https://danjuanapp.com/djapi/fund/detail/%s";
export const FUND_INFO = "https://danjuanapp.com/djapi/fund/%s";
export const FUND_GROWTH = "https://danjuanapp.com/djapi/fund/growth/%s?day=%s";
export const FUND_NAV_HISTORY = "https://danjuanapp.com/djapi/fund/nav/history/%s?page=%s&size=%s";
export const FUND_ACHIEVEMENT = "https://danjuanapp.com/djapi/fundx/base/fund/achievement/%s";
export const FUND_ASSET = "https://danjuanapp.com/djapi/fundx/base/fund/record/asset/percent?fund_code=%s";
export const FUND_MANAGER = "https://danjuanapp.com/djapi/fundx/base/fund/record/manager/list?fund_code=%s&post_status=%s";
export const FUND_TRADE_DATE = "https://danjuanapp.com/djapi/fund/order/v2/trade_date?fd_code=%s";
export const FUND_DERIVED = "https://danjuanapp.com/djapi/fund/derived/%s";

// Suggest API
export const SUGGEST_STOCK = "https://xueqiu.com/query/v1/suggest_stock.json?q=";

// 格式化URL的辅助函数
export function formatUrl(template, ...args) {
    let url = template;
    args.forEach((arg, index) => {
        url = url.replace('{}', arg).replace('%s', arg);
    });
    return url;
}
