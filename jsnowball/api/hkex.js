import { fetchHkc } from './utils.js';
import * as apiRef from './apiRef.js';

export async function northboundShareholdingSh(txtDate = null) {
    return await _getShareholding('sh', txtDate);
}

export async function northboundShareholdingSz(txtDate = null) {
    return await _getShareholding('sz', txtDate);
}

async function _getShareholding(exchange, txtDate) {
    try {
        const html = await fetchHkc(apiRef.HKEX_CONNECT + exchange.toLowerCase(), txtDate);
        const data = parseHtmlTable(html);
        return data;
    } catch (error) {
        console.error('Error fetching shareholding data:', error);
        throw error;
    }
}

/**
 * 简单的HTML表格解析函数
 * 注意：这是一个简化的实现，实际使用中建议使用 cheerio 库
 * @param {string} html - HTML字符串
 * @returns {Array<Object>} 解析后的数据
 */
function parseHtmlTable(html) {
    const data = [];
    
    try {
        // 查找表格内容的正则表达式
        const tableRegex = /<table[^>]*id="mutualmarket-result"[^>]*>[\s\S]*?<\/table>/i;
        const tableMatch = html.match(tableRegex);
        
        if (!tableMatch) {
            return data;
        }
        
        const tableHtml = tableMatch[0];
        
        // 查找所有行
        const rowRegex = /<tr[^>]*>[\s\S]*?<\/tr>/gi;
        const rows = tableHtml.match(rowRegex);
        
        if (!rows) {
            return data;
        }
        
        // 跳过表头，处理数据行
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            
            // 提取股票代码
            const codeMatch = row.match(/col-stock-code[\s\S]*?mobile-list-body[^>]*>([^<]+)</i);
            const code = codeMatch ? codeMatch[1].trim() : '';
            
            // 提取股票名称
            const nameMatch = row.match(/col-stock-name[\s\S]*?mobile-list-body[^>]*>([^<]+)</i);
            const name = nameMatch ? nameMatch[1].trim() : '';
            
            // 提取持股数量
            const shareholdingMatch = row.match(/col-shareholding[^>]*>[\s\S]*?mobile-list-body[^>]*>([^<]+)</i);
            const shareholdingStr = shareholdingMatch ? shareholdingMatch[1].trim() : '0';
            const shareholding = parseInt(shareholdingStr.replace(/,/g, '')) || 0;
            
            // 提取持股比例
            const percentMatch = row.match(/col-shareholding-percent[\s\S]*?mobile-list-body[^>]*>([^<]+)</i);
            const shareholdingPercent = percentMatch ? percentMatch[1].trim() : '';
            
            if (code && name) {
                data.push({
                    code: code,
                    name: name,
                    shareholding: shareholding,
                    shareholding_percent: shareholdingPercent
                });
            }
        }
    } catch (error) {
        console.error('Error parsing HTML table:', error);
    }
    
    return data;
}

/**
 * 港股代码转换为A股代码
 * @param {string|number} code - 港股代码
 * @returns {string} A股代码
 */
export function tranCode(code) {
    const codeStr = String(code);
    const d2 = codeStr.substring(0, 2);
    const d3 = codeStr.substring(2, 5);
    
    const codeMap = {
        '70': 'SZ000',
        '71': 'SZ001',
        '72': 'SZ002',
        '73': 'SZ003',
        '77': 'SZ300',
        '78': 'SZ301',
        '90': 'SH600',
        '91': 'SH601',
        '92': 'SH602',
        '93': 'SH603',
        '94': 'SH604',
        '95': 'SH605',
        '30': 'SH688'
    };
    
    if (codeMap[d2]) {
        return codeMap[d2] + d3;
    } else {
        throw new Error(`tran_code error: ${codeStr}`);
    }
}


