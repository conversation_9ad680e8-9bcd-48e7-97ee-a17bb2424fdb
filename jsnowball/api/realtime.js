import { fetch, fetchWithoutToken, getCurrentTimestamp } from './utils.js';
import * as apiRef from './apiRef.js';

export async function quotec(symbols) {
    const url = apiRef.REALTIME_QUOTE + symbols;
    return await fetchWithoutToken(url);
}

export async function quoteDetail(symbol) {
    const url = apiRef.REALTIME_QUOTE_DETAIL + symbol;
    return await fetch(url);
}

export async function pankou(symbol) {
    const url = apiRef.REALTIME_PANKOU + symbol;
    return await fetch(url);
}

export async function kline(symbol, period = 'day', count = 284) {
    const timestamp = getCurrentTimestamp();
    const url = apiRef.formatUrl(apiRef.KLINE, symbol, timestamp, period, count);
    return await fetch(url);
}


