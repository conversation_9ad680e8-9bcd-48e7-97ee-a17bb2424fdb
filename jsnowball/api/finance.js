import { fetch } from './utils.js';
import * as apiRef from './apiRef.js';

export async function cashFlow(symbol, isAnnals = 0, count = 10) {
    let url = apiRef.FINANCE_CASH_FLOW_URL + symbol;

    if (isAnnals === 1) {
        url += '&type=Q4';
    }

    url += '&count=' + count;

    return await fetch(url);
}

export async function indicator(symbol, isAnnals = 0, count = 10) {
    let url = apiRef.FINANCE_INDICATOR_URL + symbol;

    if (isAnnals === 1) {
        url += '&type=Q4';
    }

    url += '&count=' + count;

    return await fetch(url);
}

export async function balance(symbol, isAnnals = 0, count = 10) {
    let url = apiRef.FINANCE_BALANCE_URL + symbol;

    if (isAnnals === 1) {
        url += '&type=Q4';
    }

    url += '&count=' + count;

    return await fetch(url);
}

export async function income(symbol, isAnnals = 0, count = 10) {
    let url = apiRef.FINANCE_INCOME_URL + symbol;

    if (isAnnals === 1) {
        url += '&type=Q4';
    }

    url += '&count=' + count;

    return await fetch(url);
}

export async function business(symbol, isAnnals = 0, count = 10) {
    let url = apiRef.FINANCE_BUSINESS_URL + symbol;

    if (isAnnals === 1) {
        url += '&type=Q4';
    }

    url += '&count=' + count;

    return await fetch(url);
}


