import { fetch } from './utils.js';
import * as apiRef from './apiRef.js';

export async function skholderchg(symbol) {
    const url = apiRef.F10_SKHOLDERCHG + symbol;
    return await fetch(url);
}

export async function skholder(symbol) {
    const url = apiRef.F10_SKHOLDER + symbol;
    return await fetch(url);
}

export async function industry(symbol) {
    const url = apiRef.F10_INDUSTRY + symbol;
    return await fetch(url);
}

export async function holders(symbol) {
    const url = apiRef.F10_HOLDERS + symbol;
    return await fetch(url);
}

export async function bonus(symbol, page = 1, size = 10) {
    let url = apiRef.F10_BONUS + symbol;
    url += '&page=' + page;
    url += '&size=' + size;

    return await fetch(url);
}

export async function orgHoldingChange(symbol) {
    const url = apiRef.F10_ORG_HOLDING_CHANGE + symbol;
    return await fetch(url);
}

export async function industryCompare(symbol) {
    const url = apiRef.F10_INDUSTRY_COMPARE + symbol;
    return await fetch(url);
}

export async function businessAnalysis(symbol) {
    const url = apiRef.F10_BUSINESS_ANALYSIS + symbol;
    return await fetch(url);
}

export async function shareschg(symbol, count = 5) {
    let url = apiRef.F10_SHARESCHG + symbol;
    url += '&count=' + count;
    return await fetch(url);
}

export async function topHolders(symbol, circula = 1) {
    let url = apiRef.F10_TOP_HOLDERS + symbol;
    url += '&circula=' + circula;
    return await fetch(url);
}

export async function mainIndicator(symbol) {
    const url = apiRef.F10_INDICATOR + symbol;
    return await fetch(url);
}


