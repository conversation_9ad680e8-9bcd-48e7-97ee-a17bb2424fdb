import { fetch } from './utils.js';
import * as apiRef from './apiRef.js';

const host = "xueqiu.com";

export async function navDaily(symbol) {
    const url = apiRef.NAV_DAILY + symbol;
    return await fetch(url, host);
}

export async function rebalancingHistory(symbol, count = 20, page = 1) {
    let url = apiRef.REBALANCING_HISTORY + symbol;
    url += '&count=' + count;
    url += '&page=' + page;

    return await fetch(url, host);
}

export async function rebalancingCurrent(symbol) {
    const url = apiRef.REBALANCING_CURRENT + symbol;
    return await fetch(url, host);
}

export async function quoteCurrent(symbol) {
    const url = apiRef.QUOTE_CURRENT + symbol;
    return await fetch(url, host);
}


