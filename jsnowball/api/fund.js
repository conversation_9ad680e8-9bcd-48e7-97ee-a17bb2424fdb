import { fetchDanjuanFund } from './utils.js';
import * as apiRef from './apiRef.js';

export async function fundDetail(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_DETAIL, fundCode);
    return await fetchDanjuanFund(url);
}

export async function fundInfo(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_INFO, fundCode);
    return await fetchDanjuanFund(url);
}

export async function fundGrowth(fundCode, day = 'ty') {
    const url = apiRef.formatUrl(apiRef.FUND_GROWTH, fundCode, day);
    return await fetchDanjuanFund(url);
}

export async function fundNavHistory(fundCode, page = 1, size = 10) {
    const url = apiRef.formatUrl(apiRef.FUND_NAV_HISTORY, fundCode, page, size);
    return await fetchDanjuanFund(url);
}

export async function fundAchievement(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_ACHIEVEMENT, fundCode);
    return await fetchDanjuanFund(url);
}

export async function fundAsset(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_ASSET, fundCode);
    return await fetchDanjuanFund(url);
}

export async function fundManager(fundCode, postStatus = 1) {
    const url = apiRef.formatUrl(apiRef.FUND_MANAGER, fundCode, postStatus);
    return await fetchDanjuanFund(url);
}

export async function fundTradeDate(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_TRADE_DATE, fundCode);
    return await fetchDanjuanFund(url);
}

export async function fundDerived(fundCode) {
    const url = apiRef.formatUrl(apiRef.FUND_DERIVED, fundCode);
    return await fetchDanjuanFund(url);
}


