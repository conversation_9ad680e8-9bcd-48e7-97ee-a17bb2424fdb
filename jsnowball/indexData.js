import { fetchCsindex, formatDate } from './utils.js';
import * as apiRef from './apiRef.js';

export async function indexBasicInfo(symbols) {
    const url = apiRef.formatUrl(apiRef.INDEX_BASIC_INFO, symbols);
    return await fetchCsindex(url);
}

export async function indexDetailsData(symbols) {
    const url = apiRef.formatUrl(apiRef.INDEX_DETAILS_DATA, symbols);
    return await fetchCsindex(url);
}

export async function indexWeightTop10(symbols) {
    const url = apiRef.formatUrl(apiRef.INDEX_WEIGHT_TOP10, symbols);
    return await fetchCsindex(url);
}

export async function indexPerf7(symbols) {
    const today = new Date();
    const firstday = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const url = apiRef.formatUrl(
        apiRef.INDEX_PERF,
        symbols,
        formatDate(firstday),
        formatDate(today)
    );

    return await fetchCsindex(url);
}

export async function indexPerf30(symbols) {
    const today = new Date();
    const firstday = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    const url = apiRef.formatUrl(
        apiRef.INDEX_PERF,
        symbols,
        formatDate(firstday),
        formatDate(today)
    );

    return await fetchCsindex(url);
}

export async function indexPerf90(symbols) {
    const today = new Date();
    const firstday = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);

    const url = apiRef.formatUrl(
        apiRef.INDEX_PERF,
        symbols,
        formatDate(firstday),
        formatDate(today)
    );

    return await fetchCsindex(url);
}


