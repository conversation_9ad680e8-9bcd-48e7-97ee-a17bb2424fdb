import { fetchCsindex, formatDate } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 获取指数基本信息
 * @param {string} symbols - 指数代码
 * @returns {Promise<Object>} 指数基本信息
 */
export async function indexBasicInfo(symbols) {
    const url = apiRef.formatUrl(apiRef.INDEX_BASIC_INFO, symbols);
    return await fetchCsindex(url);
}

/**
 * 获取指数详细数据
 * @param {string} symbols - 指数代码
 * @returns {Promise<Object>} 指数详细数据
 */
export async function indexDetailsData(symbols) {
    const url = apiRef.formatUrl(apiRef.INDEX_DETAILS_DATA, symbols);
    return await fetchCsindex(url);
}

/**
 * 获取指数前十大权重股
 * @param {string} symbols - 指数代码
 * @returns {Promise<Object>} 指数前十大权重股数据
 */
export async function indexWeightTop10(symbols) {
    const url = apiRef.formatUrl(apiRef.INDEX_WEIGHT_TOP10, symbols);
    return await fetchCsindex(url);
}

/**
 * 获取指数7日表现
 * @param {string} symbols - 指数代码
 * @returns {Promise<Object>} 指数7日表现数据
 */
export async function indexPerf7(symbols) {
    const today = new Date();
    const firstday = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const url = apiRef.formatUrl(
        apiRef.INDEX_PERF,
        symbols,
        formatDate(firstday),
        formatDate(today)
    );
    
    return await fetchCsindex(url);
}

/**
 * 获取指数30日表现
 * @param {string} symbols - 指数代码
 * @returns {Promise<Object>} 指数30日表现数据
 */
export async function indexPerf30(symbols) {
    const today = new Date();
    const firstday = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    const url = apiRef.formatUrl(
        apiRef.INDEX_PERF,
        symbols,
        formatDate(firstday),
        formatDate(today)
    );
    
    return await fetchCsindex(url);
}

/**
 * 获取指数90日表现
 * @param {string} symbols - 指数代码
 * @returns {Promise<Object>} 指数90日表现数据
 */
export async function indexPerf90(symbols) {
    const today = new Date();
    const firstday = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
    
    const url = apiRef.formatUrl(
        apiRef.INDEX_PERF,
        symbols,
        formatDate(firstday),
        formatDate(today)
    );
    
    return await fetchCsindex(url);
}

/**
 * 获取指数自定义时间段表现
 * @param {string} symbols - 指数代码
 * @param {Date} startDate - 开始日期
 * @param {Date} endDate - 结束日期
 * @returns {Promise<Object>} 指数自定义时间段表现数据
 */
export async function indexPerfCustom(symbols, startDate, endDate) {
    const url = apiRef.formatUrl(
        apiRef.INDEX_PERF,
        symbols,
        formatDate(startDate),
        formatDate(endDate)
    );
    
    return await fetchCsindex(url);
}

/**
 * 获取指数完整信息
 * @param {string} symbols - 指数代码
 * @returns {Promise<Object>} 指数完整信息
 */
export async function fullIndexInfo(symbols) {
    const [
        basicInfo,
        detailsData,
        weightTop10,
        perf7,
        perf30,
        perf90
    ] = await Promise.all([
        indexBasicInfo(symbols),
        indexDetailsData(symbols),
        indexWeightTop10(symbols),
        indexPerf7(symbols),
        indexPerf30(symbols),
        indexPerf90(symbols)
    ]);
    
    return {
        basic: basicInfo,
        details: detailsData,
        weightTop10: weightTop10,
        perf7: perf7,
        perf30: perf30,
        perf90: perf90
    };
}

/**
 * 获取多个指数的基本信息
 * @param {Array<string>} symbolsList - 指数代码数组
 * @returns {Promise<Array<Object>>} 多个指数的基本信息
 */
export async function batchIndexBasicInfo(symbolsList) {
    if (!Array.isArray(symbolsList)) {
        throw new Error('symbolsList must be an array');
    }
    
    const promises = symbolsList.map(symbols => indexBasicInfo(symbols));
    return await Promise.all(promises);
}

/**
 * 获取指数概览信息
 * @param {string} symbols - 指数代码
 * @returns {Promise<Object>} 指数概览信息
 */
export async function indexOverview(symbols) {
    const [basicInfo, detailsData, perf30] = await Promise.all([
        indexBasicInfo(symbols),
        indexDetailsData(symbols),
        indexPerf30(symbols)
    ]);
    
    return {
        basic: basicInfo,
        details: detailsData,
        performance: perf30
    };
}

/**
 * 获取指数年度表现
 * @param {string} symbols - 指数代码
 * @param {number} year - 年份，默认当前年份
 * @returns {Promise<Object>} 指数年度表现数据
 */
export async function indexYearlyPerf(symbols, year = new Date().getFullYear()) {
    const startDate = new Date(year, 0, 1); // 1月1日
    const endDate = new Date(year, 11, 31); // 12月31日
    
    return await indexPerfCustom(symbols, startDate, endDate);
}
