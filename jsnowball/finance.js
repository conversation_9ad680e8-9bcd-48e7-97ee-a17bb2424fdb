import { fetch } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 获取现金流量表数据
 * @param {string} symbol - 股票代码
 * @param {number} isAnnals - 是否只获取年报数据，0为所有数据，1为年报数据
 * @param {number} count - 返回数据条数，默认10条
 * @returns {Promise<Object>} 现金流量表数据
 */
export async function cashFlow(symbol, isAnnals = 0, count = 10) {
    let url = apiRef.FINANCE_CASH_FLOW_URL + symbol;
    
    if (isAnnals === 1) {
        url += '&type=Q4';
    }
    
    url += '&count=' + count;
    
    return await fetch(url);
}

/**
 * 获取财务指标数据
 * @param {string} symbol - 股票代码
 * @param {number} isAnnals - 是否只获取年报数据，0为所有数据，1为年报数据
 * @param {number} count - 返回数据条数，默认10条
 * @returns {Promise<Object>} 财务指标数据
 */
export async function indicator(symbol, isAnnals = 0, count = 10) {
    let url = apiRef.FINANCE_INDICATOR_URL + symbol;
    
    if (isAnnals === 1) {
        url += '&type=Q4';
    }
    
    url += '&count=' + count;
    
    return await fetch(url);
}

/**
 * 获取资产负债表数据
 * @param {string} symbol - 股票代码
 * @param {number} isAnnals - 是否只获取年报数据，0为所有数据，1为年报数据
 * @param {number} count - 返回数据条数，默认10条
 * @returns {Promise<Object>} 资产负债表数据
 */
export async function balance(symbol, isAnnals = 0, count = 10) {
    let url = apiRef.FINANCE_BALANCE_URL + symbol;
    
    if (isAnnals === 1) {
        url += '&type=Q4';
    }
    
    url += '&count=' + count;
    
    return await fetch(url);
}

/**
 * 获取利润表数据
 * @param {string} symbol - 股票代码
 * @param {number} isAnnals - 是否只获取年报数据，0为所有数据，1为年报数据
 * @param {number} count - 返回数据条数，默认10条
 * @returns {Promise<Object>} 利润表数据
 */
export async function income(symbol, isAnnals = 0, count = 10) {
    let url = apiRef.FINANCE_INCOME_URL + symbol;
    
    if (isAnnals === 1) {
        url += '&type=Q4';
    }
    
    url += '&count=' + count;
    
    return await fetch(url);
}

/**
 * 获取主营业务构成数据
 * @param {string} symbol - 股票代码
 * @param {number} count - 返回数据条数，默认5条
 * @returns {Promise<Object>} 主营业务构成数据
 */
export async function business(symbol, count = 5) {
    let url = apiRef.FINANCE_BUSINESS_URL + symbol;
    url += '&count=' + count;
    
    return await fetch(url);
}

/**
 * 获取年报财务数据
 * @param {string} symbol - 股票代码
 * @param {number} count - 返回数据条数，默认10条
 * @returns {Promise<Object>} 包含所有年报财务数据的对象
 */
export async function annualFinance(symbol, count = 10) {
    const [cashFlowData, indicatorData, balanceData, incomeData] = await Promise.all([
        cashFlow(symbol, 1, count),
        indicator(symbol, 1, count),
        balance(symbol, 1, count),
        income(symbol, 1, count)
    ]);
    
    return {
        cashFlow: cashFlowData,
        indicator: indicatorData,
        balance: balanceData,
        income: incomeData
    };
}

/**
 * 获取季报财务数据
 * @param {string} symbol - 股票代码
 * @param {number} count - 返回数据条数，默认10条
 * @returns {Promise<Object>} 包含所有季报财务数据的对象
 */
export async function quarterlyFinance(symbol, count = 10) {
    const [cashFlowData, indicatorData, balanceData, incomeData] = await Promise.all([
        cashFlow(symbol, 0, count),
        indicator(symbol, 0, count),
        balance(symbol, 0, count),
        income(symbol, 0, count)
    ]);
    
    return {
        cashFlow: cashFlowData,
        indicator: indicatorData,
        balance: balanceData,
        income: incomeData
    };
}

/**
 * 获取完整的财务数据（包含业务构成）
 * @param {string} symbol - 股票代码
 * @param {number} isAnnals - 是否只获取年报数据，0为所有数据，1为年报数据
 * @param {number} count - 返回数据条数，默认10条
 * @returns {Promise<Object>} 完整的财务数据
 */
export async function fullFinance(symbol, isAnnals = 0, count = 10) {
    const [cashFlowData, indicatorData, balanceData, incomeData, businessData] = await Promise.all([
        cashFlow(symbol, isAnnals, count),
        indicator(symbol, isAnnals, count),
        balance(symbol, isAnnals, count),
        income(symbol, isAnnals, count),
        business(symbol, count)
    ]);
    
    return {
        cashFlow: cashFlowData,
        indicator: indicatorData,
        balance: balanceData,
        income: incomeData,
        business: businessData
    };
}
