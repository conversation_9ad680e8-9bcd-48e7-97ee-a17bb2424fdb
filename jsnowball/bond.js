import { fetchEastmoney } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 获取可转债信息
 * @param {number} pageSize - 每页数据条数
 * @param {number} pageCount - 页码
 * @returns {Promise<Object>} 可转债信息数据
 */
export async function convertibleBond(pageSize, pageCount) {
    const url = apiRef.formatUrl(apiRef.CONVERTIBLE_BOND, pageSize, pageCount);
    return await fetchEastmoney(url);
}

/**
 * 获取所有可转债数据
 * @param {number} maxPages - 最大页数，默认10
 * @returns {Promise<Array<Object>>} 所有可转债数据
 */
export async function allConvertibleBonds(maxPages = 10) {
    const results = [];
    
    for (let page = 1; page <= maxPages; page++) {
        try {
            const data = await convertibleBond(100, page);
            if (data && data.result && data.result.data && data.result.data.length > 0) {
                results.push(...data.result.data);
            } else {
                // 如果没有数据了，停止获取
                break;
            }
        } catch (error) {
            console.warn(`Failed to fetch page ${page}:`, error.message);
            break;
        }
    }
    
    return results;
}

/**
 * 根据条件筛选可转债
 * @param {Object} filters - 筛选条件
 * @param {number} filters.minTransferValue - 最小转股价值
 * @param {number} filters.maxTransferValue - 最大转股价值
 * @param {number} filters.minPremiumRatio - 最小溢价率
 * @param {number} filters.maxPremiumRatio - 最大溢价率
 * @param {number} filters.minCurrentPrice - 最小当前价格
 * @param {number} filters.maxCurrentPrice - 最大当前价格
 * @param {number} pageSize - 每页数据条数
 * @param {number} pageCount - 页码
 * @returns {Promise<Array<Object>>} 筛选后的可转债数据
 */
export async function filterConvertibleBonds(filters = {}, pageSize = 50, pageCount = 1) {
    const data = await convertibleBond(pageSize, pageCount);
    
    if (!data || !data.result || !data.result.data) {
        return [];
    }
    
    let bonds = data.result.data;
    
    // 应用筛选条件
    if (filters.minTransferValue !== undefined) {
        bonds = bonds.filter(bond => bond.TRANSFER_VALUE >= filters.minTransferValue);
    }
    
    if (filters.maxTransferValue !== undefined) {
        bonds = bonds.filter(bond => bond.TRANSFER_VALUE <= filters.maxTransferValue);
    }
    
    if (filters.minPremiumRatio !== undefined) {
        bonds = bonds.filter(bond => bond.TRANSFER_PREMIUM_RATIO >= filters.minPremiumRatio);
    }
    
    if (filters.maxPremiumRatio !== undefined) {
        bonds = bonds.filter(bond => bond.TRANSFER_PREMIUM_RATIO <= filters.maxPremiumRatio);
    }
    
    if (filters.minCurrentPrice !== undefined) {
        bonds = bonds.filter(bond => bond.CURRENT_BOND_PRICE >= filters.minCurrentPrice);
    }
    
    if (filters.maxCurrentPrice !== undefined) {
        bonds = bonds.filter(bond => bond.CURRENT_BOND_PRICE <= filters.maxCurrentPrice);
    }
    
    return bonds;
}

/**
 * 获取低溢价可转债
 * @param {number} maxPremiumRatio - 最大溢价率，默认10%
 * @param {number} pageSize - 每页数据条数
 * @param {number} pageCount - 页码
 * @returns {Promise<Array<Object>>} 低溢价可转债数据
 */
export async function lowPremiumBonds(maxPremiumRatio = 10, pageSize = 50, pageCount = 1) {
    return await filterConvertibleBonds({
        maxPremiumRatio: maxPremiumRatio
    }, pageSize, pageCount);
}

/**
 * 获取高转股价值可转债
 * @param {number} minTransferValue - 最小转股价值，默认100
 * @param {number} pageSize - 每页数据条数
 * @param {number} pageCount - 页码
 * @returns {Promise<Array<Object>>} 高转股价值可转债数据
 */
export async function highValueBonds(minTransferValue = 100, pageSize = 50, pageCount = 1) {
    return await filterConvertibleBonds({
        minTransferValue: minTransferValue
    }, pageSize, pageCount);
}

/**
 * 获取可转债统计信息
 * @param {number} pageSize - 每页数据条数
 * @param {number} maxPages - 最大页数
 * @returns {Promise<Object>} 可转债统计信息
 */
export async function bondStatistics(pageSize = 100, maxPages = 5) {
    const allBonds = [];
    
    for (let page = 1; page <= maxPages; page++) {
        try {
            const data = await convertibleBond(pageSize, page);
            if (data && data.result && data.result.data && data.result.data.length > 0) {
                allBonds.push(...data.result.data);
            } else {
                break;
            }
        } catch (error) {
            break;
        }
    }
    
    if (allBonds.length === 0) {
        return {
            total: 0,
            avgPremiumRatio: 0,
            avgTransferValue: 0,
            avgCurrentPrice: 0
        };
    }
    
    const total = allBonds.length;
    const avgPremiumRatio = allBonds.reduce((sum, bond) => sum + (bond.TRANSFER_PREMIUM_RATIO || 0), 0) / total;
    const avgTransferValue = allBonds.reduce((sum, bond) => sum + (bond.TRANSFER_VALUE || 0), 0) / total;
    const avgCurrentPrice = allBonds.reduce((sum, bond) => sum + (bond.CURRENT_BOND_PRICE || 0), 0) / total;
    
    return {
        total,
        avgPremiumRatio: Math.round(avgPremiumRatio * 100) / 100,
        avgTransferValue: Math.round(avgTransferValue * 100) / 100,
        avgCurrentPrice: Math.round(avgCurrentPrice * 100) / 100,
        bonds: allBonds
    };
}
