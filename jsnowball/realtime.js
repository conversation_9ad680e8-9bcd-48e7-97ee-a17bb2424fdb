import { fetch, fetchWithoutToken, getCurrentTimestamp } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 获取股票实时行情
 * @param {string} symbols - 股票代码，多个代码用逗号分隔
 * @returns {Promise<Object>} 实时行情数据
 */
export async function quotec(symbols) {
    const url = apiRef.REALTIME_QUOTE + symbols;
    return await fetchWithoutToken(url);
}

/**
 * 获取股票详细行情信息
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 详细行情数据
 */
export async function quoteDetail(symbol) {
    const url = apiRef.REALTIME_QUOTE_DETAIL + symbol;
    return await fetch(url);
}

/**
 * 获取实时分笔数据（盘口数据）
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 分笔数据
 */
export async function pankou(symbol) {
    const url = apiRef.REALTIME_PANKOU + symbol;
    return await fetch(url);
}

/**
 * 获取K线数据
 * @param {string} symbol - 股票代码
 * @param {string} period - 周期，可选值: day, week, month, quarter, year, 120m, 60m, 30m, 15m, 5m, 1m
 * @param {number} count - 数据条数，默认284
 * @returns {Promise<Object>} K线数据
 */
export async function kline(symbol, period = 'day', count = 284) {
    const timestamp = getCurrentTimestamp();
    const url = apiRef.formatUrl(apiRef.KLINE, symbol, timestamp, period, count);
    return await fetch(url);
}

/**
 * 获取分时数据
 * @param {string} symbol - 股票代码
 * @param {string} period - 周期，1m或5m
 * @param {number} count - 数据条数
 * @returns {Promise<Object>} 分时数据
 */
export async function timeSeries(symbol, period = '1m', count = 240) {
    return await kline(symbol, period, count);
}

/**
 * 获取日K线数据
 * @param {string} symbol - 股票代码
 * @param {number} count - 数据条数，默认284
 * @returns {Promise<Object>} 日K线数据
 */
export async function dailyKline(symbol, count = 284) {
    return await kline(symbol, 'day', count);
}

/**
 * 获取周K线数据
 * @param {string} symbol - 股票代码
 * @param {number} count - 数据条数，默认284
 * @returns {Promise<Object>} 周K线数据
 */
export async function weeklyKline(symbol, count = 284) {
    return await kline(symbol, 'week', count);
}

/**
 * 获取月K线数据
 * @param {string} symbol - 股票代码
 * @param {number} count - 数据条数，默认284
 * @returns {Promise<Object>} 月K线数据
 */
export async function monthlyKline(symbol, count = 284) {
    return await kline(symbol, 'month', count);
}

/**
 * 获取多只股票的实时行情
 * @param {Array<string>} symbols - 股票代码数组
 * @returns {Promise<Object>} 多只股票的实时行情数据
 */
export async function batchQuotec(symbols) {
    if (!Array.isArray(symbols)) {
        throw new Error('symbols must be an array');
    }
    const symbolsStr = symbols.join(',');
    return await quotec(symbolsStr);
}

/**
 * 获取股票的基本信息和实时价格
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 股票基本信息和实时价格
 */
export async function stockInfo(symbol) {
    const [quoteData, detailData] = await Promise.all([
        quotec(symbol),
        quoteDetail(symbol)
    ]);
    
    return {
        quote: quoteData,
        detail: detailData
    };
}
