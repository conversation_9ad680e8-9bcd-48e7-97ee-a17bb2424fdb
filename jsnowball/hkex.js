import { fetchHkc } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 获取沪股通持股数据
 * @param {string} txtDate - 日期字符串，格式为 YYYY/MM/DD，默认为今天
 * @returns {Promise<Array<Object>>} 沪股通持股数据
 */
export async function northboundShareholdingSh(txtDate = null) {
    return await getShareholding('sh', txtDate);
}

/**
 * 获取深股通持股数据
 * @param {string} txtDate - 日期字符串，格式为 YYYY/MM/DD，默认为今天
 * @returns {Promise<Array<Object>>} 深股通持股数据
 */
export async function northboundShareholdingSz(txtDate = null) {
    return await getShareholding('sz', txtDate);
}

/**
 * 获取港股通持股数据的内部方法
 * @param {string} exchange - 交易所代码 ('sh' 或 'sz')
 * @param {string} txtDate - 日期字符串
 * @returns {Promise<Array<Object>>} 持股数据
 */
async function getShareholding(exchange, txtDate) {
    try {
        const html = await fetchHkc(apiRef.HKEX_CONNECT + exchange.toLowerCase(), txtDate);
        
        // 由于 Node.js 环境中没有 DOM，我们需要使用 cheerio 来解析 HTML
        // 但为了保持简单，这里提供一个基础的字符串解析实现
        // 在实际使用中，建议安装 cheerio 库来进行 HTML 解析
        
        const data = parseHtmlTable(html);
        return data;
    } catch (error) {
        console.error('Error fetching shareholding data:', error);
        throw error;
    }
}

/**
 * 简单的HTML表格解析函数
 * 注意：这是一个简化的实现，实际使用中建议使用 cheerio 库
 * @param {string} html - HTML字符串
 * @returns {Array<Object>} 解析后的数据
 */
function parseHtmlTable(html) {
    const data = [];
    
    try {
        // 查找表格内容的正则表达式
        const tableRegex = /<table[^>]*id="mutualmarket-result"[^>]*>[\s\S]*?<\/table>/i;
        const tableMatch = html.match(tableRegex);
        
        if (!tableMatch) {
            return data;
        }
        
        const tableHtml = tableMatch[0];
        
        // 查找所有行
        const rowRegex = /<tr[^>]*>[\s\S]*?<\/tr>/gi;
        const rows = tableHtml.match(rowRegex);
        
        if (!rows) {
            return data;
        }
        
        // 跳过表头，处理数据行
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            
            // 提取股票代码
            const codeMatch = row.match(/col-stock-code[\s\S]*?mobile-list-body[^>]*>([^<]+)</i);
            const code = codeMatch ? codeMatch[1].trim() : '';
            
            // 提取股票名称
            const nameMatch = row.match(/col-stock-name[\s\S]*?mobile-list-body[^>]*>([^<]+)</i);
            const name = nameMatch ? nameMatch[1].trim() : '';
            
            // 提取持股数量
            const shareholdingMatch = row.match(/col-shareholding[^>]*>[\s\S]*?mobile-list-body[^>]*>([^<]+)</i);
            const shareholdingStr = shareholdingMatch ? shareholdingMatch[1].trim() : '0';
            const shareholding = parseInt(shareholdingStr.replace(/,/g, '')) || 0;
            
            // 提取持股比例
            const percentMatch = row.match(/col-shareholding-percent[\s\S]*?mobile-list-body[^>]*>([^<]+)</i);
            const shareholdingPercent = percentMatch ? percentMatch[1].trim() : '';
            
            if (code && name) {
                data.push({
                    code: code,
                    name: name,
                    shareholding: shareholding,
                    shareholding_percent: shareholdingPercent
                });
            }
        }
    } catch (error) {
        console.error('Error parsing HTML table:', error);
    }
    
    return data;
}

/**
 * 港股代码转换为A股代码
 * @param {string|number} code - 港股代码
 * @returns {string} A股代码
 */
export function tranCode(code) {
    const codeStr = String(code);
    const d2 = codeStr.substring(0, 2);
    const d3 = codeStr.substring(2, 5);
    
    const codeMap = {
        '70': 'SZ000',
        '71': 'SZ001',
        '72': 'SZ002',
        '73': 'SZ003',
        '77': 'SZ300',
        '78': 'SZ301',
        '90': 'SH600',
        '91': 'SH601',
        '92': 'SH602',
        '93': 'SH603',
        '94': 'SH604',
        '95': 'SH605',
        '30': 'SH688'
    };
    
    if (codeMap[d2]) {
        return codeMap[d2] + d3;
    } else {
        throw new Error(`tran_code error: ${codeStr}`);
    }
}

/**
 * 获取所有北向资金持股数据
 * @param {string} txtDate - 日期字符串，格式为 YYYY/MM/DD
 * @returns {Promise<Object>} 包含沪股通和深股通数据的对象
 */
export async function getAllNorthboundShareholding(txtDate = null) {
    const [shData, szData] = await Promise.all([
        northboundShareholdingSh(txtDate),
        northboundShareholdingSz(txtDate)
    ]);
    
    return {
        sh: shData,
        sz: szData,
        total: [...shData, ...szData]
    };
}

/**
 * 根据股票代码查找北向资金持股信息
 * @param {string} stockCode - 股票代码
 * @param {string} txtDate - 日期字符串
 * @returns {Promise<Object|null>} 持股信息或null
 */
export async function findNorthboundHolding(stockCode, txtDate = null) {
    const allData = await getAllNorthboundShareholding(txtDate);
    
    return allData.total.find(item => 
        item.code === stockCode || 
        item.name.includes(stockCode)
    ) || null;
}
