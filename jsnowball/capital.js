import { fetch } from './utils.js';
import * as apiRef from './apiRef.js';

export async function margin(symbol, page = 1, size = 180) {
    let url = apiRef.CAPITAL_MARGIN_URL + symbol;
    url += '&page=' + page;
    url += '&size=' + size;

    return await fetch(url);
}

export async function blocktrans(symbol, page = 1, size = 30) {
    let url = apiRef.CAPITAL_BLOCKTRANS_URL + symbol;
    url += '&page=' + page;
    url += '&size=' + size;

    return await fetch(url);
}

export async function capitalAssort(symbol) {
    const url = apiRef.CAPITAL_ASSORT_URL + symbol;
    return await fetch(url);
}

export async function capitalFlow(symbol) {
    const url = apiRef.CAPITAL_FLOW_URL + symbol;
    return await fetch(url);
}

export async function capitalHistory(symbol, count = 20) {
    let url = apiRef.CAPITAL_HISTORY_URL + symbol;
    url += '&count=' + count;

    return await fetch(url);
}


