import { fetch } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 获取融资融券数据
 * @param {string} symbol - 股票代码
 * @param {number} page - 页码，默认1
 * @param {number} size - 每页数据条数，默认180
 * @returns {Promise<Object>} 融资融券数据
 */
export async function margin(symbol, page = 1, size = 180) {
    let url = apiRef.CAPITAL_MARGIN_URL + symbol;
    url += '&page=' + page;
    url += '&size=' + size;
    
    return await fetch(url);
}

/**
 * 获取大宗交易数据
 * @param {string} symbol - 股票代码
 * @param {number} page - 页码，默认1
 * @param {number} size - 每页数据条数，默认30
 * @returns {Promise<Object>} 大宗交易数据
 */
export async function blocktrans(symbol, page = 1, size = 30) {
    let url = apiRef.CAPITAL_BLOCKTRANS_URL + symbol;
    url += '&page=' + page;
    url += '&size=' + size;
    
    return await fetch(url);
}

/**
 * 获取资金分布数据
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 资金分布数据
 */
export async function capitalAssort(symbol) {
    const url = apiRef.CAPITAL_ASSORT_URL + symbol;
    return await fetch(url);
}

/**
 * 获取资金流向数据
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 资金流向数据
 */
export async function capitalFlow(symbol) {
    const url = apiRef.CAPITAL_FLOW_URL + symbol;
    return await fetch(url);
}

/**
 * 获取历史资金流向数据
 * @param {string} symbol - 股票代码
 * @param {number} count - 返回数据条数，默认20条
 * @returns {Promise<Object>} 历史资金流向数据
 */
export async function capitalHistory(symbol, count = 20) {
    let url = apiRef.CAPITAL_HISTORY_URL + symbol;
    url += '&count=' + count;
    
    return await fetch(url);
}

/**
 * 获取完整的资本数据
 * @param {string} symbol - 股票代码
 * @param {Object} options - 可选参数
 * @param {number} options.marginPage - 融资融券页码
 * @param {number} options.marginSize - 融资融券每页数据条数
 * @param {number} options.blocktransPage - 大宗交易页码
 * @param {number} options.blocktransSize - 大宗交易每页数据条数
 * @param {number} options.historyCount - 历史资金流向数据条数
 * @returns {Promise<Object>} 完整的资本数据
 */
export async function fullCapital(symbol, options = {}) {
    const {
        marginPage = 1,
        marginSize = 180,
        blocktransPage = 1,
        blocktransSize = 30,
        historyCount = 20
    } = options;
    
    const [marginData, blocktransData, assortData, flowData, historyData] = await Promise.all([
        margin(symbol, marginPage, marginSize),
        blocktrans(symbol, blocktransPage, blocktransSize),
        capitalAssort(symbol),
        capitalFlow(symbol),
        capitalHistory(symbol, historyCount)
    ]);
    
    return {
        margin: marginData,
        blocktrans: blocktransData,
        assort: assortData,
        flow: flowData,
        history: historyData
    };
}

/**
 * 获取最近的融资融券数据
 * @param {string} symbol - 股票代码
 * @param {number} days - 最近天数，默认30天
 * @returns {Promise<Object>} 最近的融资融券数据
 */
export async function recentMargin(symbol, days = 30) {
    return await margin(symbol, 1, days);
}

/**
 * 获取最近的大宗交易数据
 * @param {string} symbol - 股票代码
 * @param {number} count - 数据条数，默认10条
 * @returns {Promise<Object>} 最近的大宗交易数据
 */
export async function recentBlocktrans(symbol, count = 10) {
    return await blocktrans(symbol, 1, count);
}

/**
 * 获取资金流向概览
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 资金流向概览数据
 */
export async function capitalOverview(symbol) {
    const [assortData, flowData] = await Promise.all([
        capitalAssort(symbol),
        capitalFlow(symbol)
    ]);
    
    return {
        assort: assortData,
        flow: flowData
    };
}
