import { fetch } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 获取最新研报数据
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 最新研报数据
 */
export async function report(symbol) {
    if (!symbol) {
        throw new Error('Stock symbol is required');
    }
    
    const url = apiRef.REPORT_LATEST_URL + symbol;
    return await fetch(url);
}

/**
 * 获取盈利预测数据
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 盈利预测数据
 */
export async function earningforecast(symbol) {
    if (!symbol) {
        throw new Error('Stock symbol is required');
    }
    
    const url = apiRef.REPORT_EARNINGFORECAST_URL + symbol;
    return await fetch(url);
}

/**
 * 获取完整的研报和预测数据
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 完整的研报和预测数据
 */
export async function fullReport(symbol) {
    const [reportData, forecastData] = await Promise.all([
        report(symbol),
        earningforecast(symbol)
    ]);
    
    return {
        report: reportData,
        forecast: forecastData
    };
}

/**
 * 获取多只股票的研报数据
 * @param {Array<string>} symbols - 股票代码数组
 * @returns {Promise<Array<Object>>} 多只股票的研报数据
 */
export async function batchReports(symbols) {
    if (!Array.isArray(symbols)) {
        throw new Error('symbols must be an array');
    }
    
    const promises = symbols.map(symbol => report(symbol));
    return await Promise.all(promises);
}

/**
 * 获取多只股票的盈利预测数据
 * @param {Array<string>} symbols - 股票代码数组
 * @returns {Promise<Array<Object>>} 多只股票的盈利预测数据
 */
export async function batchEarningForecasts(symbols) {
    if (!Array.isArray(symbols)) {
        throw new Error('symbols must be an array');
    }
    
    const promises = symbols.map(symbol => earningforecast(symbol));
    return await Promise.all(promises);
}

/**
 * 获取多只股票的完整研报数据
 * @param {Array<string>} symbols - 股票代码数组
 * @returns {Promise<Array<Object>>} 多只股票的完整研报数据
 */
export async function batchFullReports(symbols) {
    if (!Array.isArray(symbols)) {
        throw new Error('symbols must be an array');
    }
    
    const promises = symbols.map(symbol => fullReport(symbol));
    return await Promise.all(promises);
}

/**
 * 获取研报概览信息
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 研报概览信息
 */
export async function reportOverview(symbol) {
    try {
        const [reportData, forecastData] = await Promise.all([
            report(symbol),
            earningforecast(symbol)
        ]);
        
        // 提取关键信息
        const overview = {
            symbol: symbol,
            hasReport: !!(reportData && reportData.data && reportData.data.length > 0),
            hasForecast: !!(forecastData && forecastData.data && forecastData.data.length > 0),
            reportCount: reportData && reportData.data ? reportData.data.length : 0,
            forecastCount: forecastData && forecastData.data ? forecastData.data.length : 0
        };
        
        // 如果有研报数据，提取最新的研报信息
        if (overview.hasReport) {
            const latestReport = reportData.data[0];
            overview.latestReport = {
                title: latestReport.title,
                rating: latestReport.rating,
                target_price: latestReport.target_price,
                publish_time: latestReport.publish_time,
                author: latestReport.author,
                org_name: latestReport.org_name
            };
        }
        
        // 如果有预测数据，提取最新的预测信息
        if (overview.hasForecast) {
            const latestForecast = forecastData.data[0];
            overview.latestForecast = {
                year: latestForecast.year,
                eps: latestForecast.eps,
                pe: latestForecast.pe,
                revenue: latestForecast.revenue,
                profit: latestForecast.profit
            };
        }
        
        return overview;
    } catch (error) {
        return {
            symbol: symbol,
            hasReport: false,
            hasForecast: false,
            reportCount: 0,
            forecastCount: 0,
            error: error.message
        };
    }
}

/**
 * 根据评级筛选研报
 * @param {string} symbol - 股票代码
 * @param {string} rating - 评级筛选条件（如：'买入', '增持', '中性', '减持', '卖出'）
 * @returns {Promise<Array<Object>>} 筛选后的研报数据
 */
export async function filterReportsByRating(symbol, rating) {
    const reportData = await report(symbol);
    
    if (!reportData || !reportData.data) {
        return [];
    }
    
    return reportData.data.filter(report => 
        report.rating && report.rating.includes(rating)
    );
}
