import { fetchWithoutToken } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 根据关键词搜索股票代码
 * @param {string} query - 搜索关键词
 * @returns {Promise<Object>} 搜索结果
 */
export async function suggestStock(query) {
    if (!query || typeof query !== 'string') {
        throw new Error('Query must be a non-empty string');
    }
    
    const url = apiRef.SUGGEST_STOCK + encodeURIComponent(query);
    return await fetchWithoutToken(url, 'xueqiu.com');
}

/**
 * 搜索股票并返回格式化结果
 * @param {string} query - 搜索关键词
 * @returns {Promise<Array<Object>>} 格式化的搜索结果
 */
export async function searchStocks(query) {
    const result = await suggestStock(query);
    
    if (!result || !result.data) {
        return [];
    }
    
    return result.data.map(item => ({
        symbol: item.code,
        name: item.name,
        type: item.type,
        exchange: item.exchange,
        fullName: `${item.code} ${item.name}`
    }));
}

/**
 * 根据股票名称搜索
 * @param {string} name - 股票名称
 * @returns {Promise<Array<Object>>} 搜索结果
 */
export async function searchByName(name) {
    return await searchStocks(name);
}

/**
 * 根据股票代码搜索
 * @param {string} code - 股票代码
 * @returns {Promise<Array<Object>>} 搜索结果
 */
export async function searchByCode(code) {
    return await searchStocks(code);
}

/**
 * 模糊搜索股票
 * @param {string} keyword - 搜索关键词
 * @param {Object} options - 搜索选项
 * @param {string} options.type - 股票类型筛选
 * @param {string} options.exchange - 交易所筛选
 * @param {number} options.limit - 结果数量限制
 * @returns {Promise<Array<Object>>} 筛选后的搜索结果
 */
export async function fuzzySearch(keyword, options = {}) {
    const { type, exchange, limit } = options;
    
    let results = await searchStocks(keyword);
    
    // 按类型筛选
    if (type) {
        results = results.filter(item => item.type === type);
    }
    
    // 按交易所筛选
    if (exchange) {
        results = results.filter(item => item.exchange === exchange);
    }
    
    // 限制结果数量
    if (limit && limit > 0) {
        results = results.slice(0, limit);
    }
    
    return results;
}

/**
 * 搜索A股股票
 * @param {string} keyword - 搜索关键词
 * @param {number} limit - 结果数量限制
 * @returns {Promise<Array<Object>>} A股搜索结果
 */
export async function searchAShares(keyword, limit = 10) {
    return await fuzzySearch(keyword, {
        exchange: 'SH,SZ',
        limit: limit
    });
}

/**
 * 搜索港股
 * @param {string} keyword - 搜索关键词
 * @param {number} limit - 结果数量限制
 * @returns {Promise<Array<Object>>} 港股搜索结果
 */
export async function searchHKStocks(keyword, limit = 10) {
    return await fuzzySearch(keyword, {
        exchange: 'HK',
        limit: limit
    });
}

/**
 * 搜索美股
 * @param {string} keyword - 搜索关键词
 * @param {number} limit - 结果数量限制
 * @returns {Promise<Array<Object>>} 美股搜索结果
 */
export async function searchUSStocks(keyword, limit = 10) {
    return await fuzzySearch(keyword, {
        exchange: 'US',
        limit: limit
    });
}

/**
 * 批量搜索股票
 * @param {Array<string>} keywords - 搜索关键词数组
 * @returns {Promise<Array<Array<Object>>>} 批量搜索结果
 */
export async function batchSearch(keywords) {
    if (!Array.isArray(keywords)) {
        throw new Error('keywords must be an array');
    }
    
    const promises = keywords.map(keyword => searchStocks(keyword));
    return await Promise.all(promises);
}

/**
 * 智能搜索（自动判断输入类型）
 * @param {string} input - 输入内容
 * @returns {Promise<Array<Object>>} 搜索结果
 */
export async function smartSearch(input) {
    if (!input || typeof input !== 'string') {
        return [];
    }
    
    const trimmedInput = input.trim();
    
    // 如果输入看起来像股票代码（包含数字）
    if (/\d/.test(trimmedInput)) {
        return await searchByCode(trimmedInput);
    } else {
        // 否则按名称搜索
        return await searchByName(trimmedInput);
    }
}
