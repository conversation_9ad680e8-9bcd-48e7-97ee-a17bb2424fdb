/**
 * jsnowball - JavaScript version of pysnowball
 * Xueqiu (雪球) API client using ESM modules and axios
 *
 * <AUTHOR>
 * @version 1.0.0
 */

// Token management
export { getToken, setToken, clearToken, hasToken, getTokenInfo } from './api/token.js';

// Finance data
export {
    cashFlow,
    indicator,
    balance,
    income,
    business
} from './api/finance.js';

// Real-time data
export {
    quotec,
    quoteDetail,
    pankou,
    kline
} from './api/realtime.js';

// Capital flow data
export {
    margin,
    blocktrans,
    capitalAssort,
    capitalFlow,
    capitalHistory
} from './api/capital.js';

// F10 data
export {
    skholderchg,
    skholder,
    industry,
    holders,
    bonus,
    orgHoldingChange,
    industryCompare,
    businessAnalysis,
    shareschg,
    topHolders,
    mainIndicator
} from './api/f10.js';

// Report data
export {
    report,
    earningforecast
} from './api/report.js';

// Cube data
export {
    navDaily,
    rebalancingHistory,
    rebalancingCurrent,
    quoteCurrent
} from './api/cube.js';

// Fund data
export {
    fundDetail,
    fundInfo,
    fundGrowth,
    fundNavHistory,
    fundAchievement,
    fundAsset,
    fundManager,
    fundTradeDate,
    fundDerived
} from './api/fund.js';

// Bond data
export {
    convertibleBond
} from './api/bond.js';

// Index data
export {
    indexBasicInfo,
    indexDetailsData,
    indexWeightTop10,
    indexPerf7,
    indexPerf30,
    indexPerf90
} from './api/indexData.js';

// User data
export {
    watchList,
    watchStock
} from './api/user.js';

// Search and suggest
export {
    suggestStock
} from './api/suggest.js';

// HKEX data
export {
    northboundShareholdingSh,
    northboundShareholdingSz,
    tranCode
} from './api/hkex.js';

// Utility functions
export {
    fetch,
    fetchWithoutToken,
    fetchEastmoney,
    fetchDanjuanFund,
    fetchCsindex,
    fetchHkc,
    formatDate,
    formatDateSlash,
    getCurrentTimestamp
} from './api/utils.js';

// API references
export * as apiRef from './api/apiRef.js';

// Import functions for default export
import { setToken as _setToken, getToken as _getToken, hasToken as _hasToken } from './api/token.js';
import { quotec as _quotec, pankou as _pankou, kline as _kline } from './api/realtime.js';
import { cashFlow as _cashFlow, balance as _balance, income as _income, indicator as _indicator, business as _business } from './api/finance.js';
import { suggestStock as _suggestStock } from './api/suggest.js';
import { bonus as _bonus, holders as _holders, industry as _industry } from './api/f10.js';
import { margin as _margin, capitalFlow as _capitalFlow } from './api/capital.js';
import { fundInfo as _fundInfo, fundDetail as _fundDetail } from './api/fund.js';
import { convertibleBond as _convertibleBond } from './api/bond.js';
import { report as _report, earningforecast as _earningforecast } from './api/report.js';

// Default export with commonly used functions
export default {
    // Token
    setToken: _setToken,
    getToken: _getToken,
    hasToken: _hasToken,

    // Real-time
    quotec: _quotec,
    pankou: _pankou,
    kline: _kline,

    // Finance
    cashFlow: _cashFlow,
    balance: _balance,
    income: _income,
    indicator: _indicator,
    business: _business,

    // Search
    suggestStock: _suggestStock,

    // F10
    bonus: _bonus,
    holders: _holders,
    industry: _industry,

    // Capital
    margin: _margin,
    capitalFlow: _capitalFlow,

    // Fund
    fundInfo: _fundInfo,
    fundDetail: _fundDetail,

    // Bond
    convertibleBond: _convertibleBond,

    // Report
    report: _report,
    earningforecast: _earningforecast
};

// Version info
export const version = '1.0.0';
export const name = 'jsnowball';
