/**
 * jsnowball - JavaScript version of pysnowball
 * <PERSON><PERSON><PERSON>u (雪球) API client using ESM modules and axios
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// Token management
export { getToken, setToken, clearToken, hasToken, getTokenInfo } from './token.js';

// Finance data
export {
    cashFlow,
    indicator,
    balance,
    income,
    business,
    annualFinance,
    quarterlyFinance,
    fullFinance
} from './finance.js';

// Real-time data
export {
    quotec,
    quoteDetail,
    pankou,
    kline,
    timeSeries,
    dailyKline,
    weeklyKline,
    monthlyKline,
    batchQuotec,
    stockInfo
} from './realtime.js';

// Capital flow data
export {
    margin,
    blocktrans,
    capitalAssort,
    capitalFlow,
    capitalHistory,
    fullCapital,
    recentMargin,
    recentBlocktrans,
    capitalOverview
} from './capital.js';

// F10 data
export {
    skholderchg,
    skholder,
    industry,
    holders,
    bonus,
    orgHoldingChange,
    industryCompare,
    businessAnalysis,
    shareschg,
    topHolders,
    mainIndicator,
    fullF10,
    shareholderInfo,
    industryInfo
} from './f10.js';

// Report data
export {
    report,
    earningforecast,
    fullReport,
    batchReports,
    batchEarningForecasts,
    batchFullReports,
    reportOverview,
    filterReportsByRating
} from './report.js';

// Cube data
export {
    navDaily,
    rebalancingHistory,
    rebalancingCurrent,
    quoteCurrent,
    fullCubeInfo,
    batchQuoteCurrent,
    cubeOverview
} from './cube.js';

// Fund data
export {
    fundDetail,
    fundInfo,
    fundGrowth,
    fundNavHistory,
    fundAchievement,
    fundAsset,
    fundManager,
    fundTradeDate,
    fundDerived,
    fullFundInfo,
    fundOverview,
    batchFundInfo
} from './fund.js';

// Bond data
export {
    convertibleBond,
    allConvertibleBonds,
    filterConvertibleBonds,
    lowPremiumBonds,
    highValueBonds,
    bondStatistics
} from './bond.js';

// Index data
export {
    indexBasicInfo,
    indexDetailsData,
    indexWeightTop10,
    indexPerf7,
    indexPerf30,
    indexPerf90,
    indexPerfCustom,
    fullIndexInfo,
    batchIndexBasicInfo,
    indexOverview,
    indexYearlyPerf
} from './indexData.js';

// User data
export {
    watchList,
    watchStock,
    allWatchStocks,
    defaultWatchStocks,
    watchStocksByName,
    watchStocksOverview
} from './user.js';

// Search and suggest
export {
    suggestStock,
    searchStocks,
    searchByName,
    searchByCode,
    fuzzySearch,
    searchAShares,
    searchHKStocks,
    searchUSStocks,
    batchSearch,
    smartSearch
} from './suggest.js';

// HKEX data
export {
    northboundShareholdingSh,
    northboundShareholdingSz,
    tranCode,
    getAllNorthboundShareholding,
    findNorthboundHolding
} from './hkex.js';

// Utility functions
export {
    fetch,
    fetchWithoutToken,
    fetchEastmoney,
    fetchDanjuanFund,
    fetchCsindex,
    fetchHkc,
    buildQueryString,
    formatDate,
    formatDateSlash,
    getCurrentTimestamp
} from './utils.js';

// API references
export * as apiRef from './apiRef.js';

// Import functions for default export
import { setToken as _setToken, getToken as _getToken, hasToken as _hasToken } from './token.js';
import { quotec as _quotec, pankou as _pankou, kline as _kline } from './realtime.js';
import { cashFlow as _cashFlow, balance as _balance, income as _income, indicator as _indicator, business as _business } from './finance.js';
import { suggestStock as _suggestStock, searchStocks as _searchStocks } from './suggest.js';
import { bonus as _bonus, holders as _holders, industry as _industry } from './f10.js';
import { margin as _margin, capitalFlow as _capitalFlow } from './capital.js';
import { fundInfo as _fundInfo, fundDetail as _fundDetail } from './fund.js';
import { convertibleBond as _convertibleBond } from './bond.js';
import { report as _report, earningforecast as _earningforecast } from './report.js';

// Default export with commonly used functions
export default {
    // Token
    setToken: _setToken,
    getToken: _getToken,
    hasToken: _hasToken,

    // Real-time
    quotec: _quotec,
    pankou: _pankou,
    kline: _kline,

    // Finance
    cashFlow: _cashFlow,
    balance: _balance,
    income: _income,
    indicator: _indicator,
    business: _business,

    // Search
    suggestStock: _suggestStock,
    searchStocks: _searchStocks,

    // F10
    bonus: _bonus,
    holders: _holders,
    industry: _industry,

    // Capital
    margin: _margin,
    capitalFlow: _capitalFlow,

    // Fund
    fundInfo: _fundInfo,
    fundDetail: _fundDetail,

    // Bond
    convertibleBond: _convertibleBond,

    // Report
    report: _report,
    earningforecast: _earningforecast
};

// Version info
export const version = '1.0.0';
export const name = 'jsnowball';
