/**
 * jsnowball 完整测试脚本
 * 测试所有API方法
 */

import * as ball from './index.js';

// 测试用的股票代码
const TEST_SYMBOL = 'SZ000001'; // 平安银行
const TEST_FUND_CODE = '000001'; // 华夏成长混合
const TEST_CUBE_SYMBOL = 'ZH123456'; // 测试组合代码
const TEST_INDEX_CODE = '000001'; // 上证指数

async function testSuggestStock() {
    console.log('📊 测试 suggestStock');
    try {
        const result = await ball.suggestStock('平安银行');
        console.log('✅ suggestStock 成功');
        return result;
    } catch (error) {
        console.error('❌ suggestStock 失败:', error.message);
        return null;
    }
}

async function testFinance() {
    console.log('\n💰 测试 Finance 模块');

    // 测试 cashFlow
    try {
        const result = await ball.cashFlow(TEST_SYMBOL, 0, 5);
        console.log('✅ cashFlow 成功');
    } catch (error) {
        console.error('❌ cashFlow 失败:', error.message);
    }

    // 测试 indicator
    try {
        const result = await ball.indicator(TEST_SYMBOL, 0, 5);
        console.log('✅ indicator 成功');
    } catch (error) {
        console.error('❌ indicator 失败:', error.message);
    }

    // 测试 balance
    try {
        const result = await ball.balance(TEST_SYMBOL, 0, 5);
        console.log('✅ balance 成功');
    } catch (error) {
        console.error('❌ balance 失败:', error.message);
    }

    // 测试 income
    try {
        const result = await ball.income(TEST_SYMBOL, 0, 5);
        console.log('✅ income 成功');
    } catch (error) {
        console.error('❌ income 失败:', error.message);
    }

    // 测试 business
    try {
        const result = await ball.business(TEST_SYMBOL, 0, 5);
        console.log('✅ business 成功');
    } catch (error) {
        console.error('❌ business 失败:', error.message);
    }
}

async function testRealtime() {
    console.log('\n📈 测试 Realtime 模块');

    // 测试 quotec
    try {
        const result = await ball.quotec(TEST_SYMBOL);
        console.log('✅ quotec 成功');
    } catch (error) {
        console.error('❌ quotec 失败:', error.message);
    }

    // 测试 quoteDetail
    try {
        const result = await ball.quoteDetail(TEST_SYMBOL);
        console.log('✅ quoteDetail 成功');
    } catch (error) {
        console.error('❌ quoteDetail 失败:', error.message);
    }

    // 测试 pankou
    try {
        const result = await ball.pankou(TEST_SYMBOL);
        console.log('✅ pankou 成功');
    } catch (error) {
        console.error('❌ pankou 失败:', error.message);
    }

    // 测试 kline
    try {
        const result = await ball.kline(TEST_SYMBOL, 'day', 10);
        console.log('✅ kline 成功');
    } catch (error) {
        console.error('❌ kline 失败:', error.message);
    }
}

async function testCapital() {
    console.log('\n💸 测试 Capital 模块');

    // 测试 margin
    try {
        const result = await ball.margin(TEST_SYMBOL, 1, 10);
        console.log('✅ margin 成功');
    } catch (error) {
        console.error('❌ margin 失败:', error.message);
    }

    // 测试 blocktrans
    try {
        const result = await ball.blocktrans(TEST_SYMBOL, 1, 10);
        console.log('✅ blocktrans 成功');
    } catch (error) {
        console.error('❌ blocktrans 失败:', error.message);
    }

    // 测试 capitalAssort
    try {
        const result = await ball.capitalAssort(TEST_SYMBOL);
        console.log('✅ capitalAssort 成功');
    } catch (error) {
        console.error('❌ capitalAssort 失败:', error.message);
    }

    // 测试 capitalFlow
    try {
        const result = await ball.capitalFlow(TEST_SYMBOL);
        console.log('✅ capitalFlow 成功');
    } catch (error) {
        console.error('❌ capitalFlow 失败:', error.message);
    }

    // 测试 capitalHistory
    try {
        const result = await ball.capitalHistory(TEST_SYMBOL, 10);
        console.log('✅ capitalHistory 成功');
    } catch (error) {
        console.error('❌ capitalHistory 失败:', error.message);
    }
}

async function testF10() {
    console.log('\n📋 测试 F10 模块');

    // 测试 skholderchg
    try {
        const result = await ball.skholderchg(TEST_SYMBOL);
        console.log('✅ skholderchg 成功');
    } catch (error) {
        console.error('❌ skholderchg 失败:', error.message);
    }

    // 测试 skholder
    try {
        const result = await ball.skholder(TEST_SYMBOL);
        console.log('✅ skholder 成功');
    } catch (error) {
        console.error('❌ skholder 失败:', error.message);
    }

    // 测试 industry
    try {
        const result = await ball.industry(TEST_SYMBOL);
        console.log('✅ industry 成功');
    } catch (error) {
        console.error('❌ industry 失败:', error.message);
    }

    // 测试 holders
    try {
        const result = await ball.holders(TEST_SYMBOL);
        console.log('✅ holders 成功');
    } catch (error) {
        console.error('❌ holders 失败:', error.message);
    }

    // 测试 bonus
    try {
        const result = await ball.bonus(TEST_SYMBOL, 1, 5);
        console.log('✅ bonus 成功');
    } catch (error) {
        console.error('❌ bonus 失败:', error.message);
    }

    // 测试 orgHoldingChange
    try {
        const result = await ball.orgHoldingChange(TEST_SYMBOL);
        console.log('✅ orgHoldingChange 成功');
    } catch (error) {
        console.error('❌ orgHoldingChange 失败:', error.message);
    }

    // 测试 industryCompare
    try {
        const result = await ball.industryCompare(TEST_SYMBOL);
        console.log('✅ industryCompare 成功');
    } catch (error) {
        console.error('❌ industryCompare 失败:', error.message);
    }

    // 测试 businessAnalysis
    try {
        const result = await ball.businessAnalysis(TEST_SYMBOL);
        console.log('✅ businessAnalysis 成功');
    } catch (error) {
        console.error('❌ businessAnalysis 失败:', error.message);
    }

    // 测试 shareschg
    try {
        const result = await ball.shareschg(TEST_SYMBOL, 5);
        console.log('✅ shareschg 成功');
    } catch (error) {
        console.error('❌ shareschg 失败:', error.message);
    }

    // 测试 topHolders
    try {
        const result = await ball.topHolders(TEST_SYMBOL, 1);
        console.log('✅ topHolders 成功');
    } catch (error) {
        console.error('❌ topHolders 失败:', error.message);
    }

    // 测试 mainIndicator
    try {
        const result = await ball.mainIndicator(TEST_SYMBOL);
        console.log('✅ mainIndicator 成功');
    } catch (error) {
        console.error('❌ mainIndicator 失败:', error.message);
    }
}

async function testReport() {
    console.log('\n📰 测试 Report 模块');

    // 测试 report
    try {
        const result = await ball.report(TEST_SYMBOL);
        console.log('✅ report 成功');
    } catch (error) {
        console.error('❌ report 失败:', error.message);
    }

    // 测试 earningforecast
    try {
        const result = await ball.earningforecast(TEST_SYMBOL);
        console.log('✅ earningforecast 成功');
    } catch (error) {
        console.error('❌ earningforecast 失败:', error.message);
    }
}

async function testCube() {
    console.log('\n🧊 测试 Cube 模块');

    // 测试 navDaily
    try {
        const result = await ball.navDaily(TEST_CUBE_SYMBOL);
        console.log('✅ navDaily 成功');
    } catch (error) {
        console.error('❌ navDaily 失败:', error.message);
    }

    // 测试 rebalancingHistory
    try {
        const result = await ball.rebalancingHistory(TEST_CUBE_SYMBOL, 10, 1);
        console.log('✅ rebalancingHistory 成功');
    } catch (error) {
        console.error('❌ rebalancingHistory 失败:', error.message);
    }

    // 测试 rebalancingCurrent
    try {
        const result = await ball.rebalancingCurrent(TEST_CUBE_SYMBOL);
        console.log('✅ rebalancingCurrent 成功');
    } catch (error) {
        console.error('❌ rebalancingCurrent 失败:', error.message);
    }

    // 测试 quoteCurrent
    try {
        const result = await ball.quoteCurrent(TEST_CUBE_SYMBOL);
        console.log('✅ quoteCurrent 成功');
    } catch (error) {
        console.error('❌ quoteCurrent 失败:', error.message);
    }
}

async function testFund() {
    console.log('\n🏦 测试 Fund 模块');

    // 测试 fundDetail
    try {
        const result = await ball.fundDetail(TEST_FUND_CODE);
        console.log('✅ fundDetail 成功');
    } catch (error) {
        console.error('❌ fundDetail 失败:', error.message);
    }

    // 测试 fundInfo
    try {
        const result = await ball.fundInfo(TEST_FUND_CODE);
        console.log('✅ fundInfo 成功');
    } catch (error) {
        console.error('❌ fundInfo 失败:', error.message);
    }

    // 测试 fundGrowth
    try {
        const result = await ball.fundGrowth(TEST_FUND_CODE, 'ty');
        console.log('✅ fundGrowth 成功');
    } catch (error) {
        console.error('❌ fundGrowth 失败:', error.message);
    }

    // 测试 fundNavHistory
    try {
        const result = await ball.fundNavHistory(TEST_FUND_CODE, 1, 5);
        console.log('✅ fundNavHistory 成功');
    } catch (error) {
        console.error('❌ fundNavHistory 失败:', error.message);
    }

    // 测试 fundAchievement
    try {
        const result = await ball.fundAchievement(TEST_FUND_CODE);
        console.log('✅ fundAchievement 成功');
    } catch (error) {
        console.error('❌ fundAchievement 失败:', error.message);
    }

    // 测试 fundAsset
    try {
        const result = await ball.fundAsset(TEST_FUND_CODE);
        console.log('✅ fundAsset 成功');
    } catch (error) {
        console.error('❌ fundAsset 失败:', error.message);
    }

    // 测试 fundManager
    try {
        const result = await ball.fundManager(TEST_FUND_CODE, 1);
        console.log('✅ fundManager 成功');
    } catch (error) {
        console.error('❌ fundManager 失败:', error.message);
    }

    // 测试 fundTradeDate
    try {
        const result = await ball.fundTradeDate(TEST_FUND_CODE);
        console.log('✅ fundTradeDate 成功');
    } catch (error) {
        console.error('❌ fundTradeDate 失败:', error.message);
    }

    // 测试 fundDerived
    try {
        const result = await ball.fundDerived(TEST_FUND_CODE);
        console.log('✅ fundDerived 成功');
    } catch (error) {
        console.error('❌ fundDerived 失败:', error.message);
    }
}

async function testBond() {
    console.log('\n💎 测试 Bond 模块');

    // 测试 convertibleBond
    try {
        const result = await ball.convertibleBond(10, 1);
        console.log('✅ convertibleBond 成功');
    } catch (error) {
        console.error('❌ convertibleBond 失败:', error.message);
    }
}

async function testIndexData() {
    console.log('\n📊 测试 IndexData 模块');

    // 测试 indexBasicInfo
    try {
        const result = await ball.indexBasicInfo(TEST_INDEX_CODE);
        console.log('✅ indexBasicInfo 成功');
    } catch (error) {
        console.error('❌ indexBasicInfo 失败:', error.message);
    }

    // 测试 indexDetailsData
    try {
        const result = await ball.indexDetailsData(TEST_INDEX_CODE);
        console.log('✅ indexDetailsData 成功');
    } catch (error) {
        console.error('❌ indexDetailsData 失败:', error.message);
    }

    // 测试 indexWeightTop10
    try {
        const result = await ball.indexWeightTop10(TEST_INDEX_CODE);
        console.log('✅ indexWeightTop10 成功');
    } catch (error) {
        console.error('❌ indexWeightTop10 失败:', error.message);
    }

    // 测试 indexPerf7
    try {
        const result = await ball.indexPerf7(TEST_INDEX_CODE);
        console.log('✅ indexPerf7 成功');
    } catch (error) {
        console.error('❌ indexPerf7 失败:', error.message);
    }

    // 测试 indexPerf30
    try {
        const result = await ball.indexPerf30(TEST_INDEX_CODE);
        console.log('✅ indexPerf30 成功');
    } catch (error) {
        console.error('❌ indexPerf30 失败:', error.message);
    }

    // 测试 indexPerf90
    try {
        const result = await ball.indexPerf90(TEST_INDEX_CODE);
        console.log('✅ indexPerf90 成功');
    } catch (error) {
        console.error('❌ indexPerf90 失败:', error.message);
    }
}

async function testUser() {
    console.log('\n👤 测试 User 模块');

    // 测试 watchList
    try {
        const result = await ball.watchList();
        console.log('✅ watchList 成功');
    } catch (error) {
        console.error('❌ watchList 失败:', error.message);
    }

    // 测试 watchStock
    try {
        const result = await ball.watchStock('1');
        console.log('✅ watchStock 成功');
    } catch (error) {
        console.error('❌ watchStock 失败:', error.message);
    }
}

async function testHkex() {
    console.log('\n🇭🇰 测试 HKEX 模块');

    // 测试 northboundShareholdingSh
    try {
        const result = await ball.northboundShareholdingSh();
        console.log('✅ northboundShareholdingSh 成功');
    } catch (error) {
        console.error('❌ northboundShareholdingSh 失败:', error.message);
    }

    // 测试 northboundShareholdingSz
    try {
        const result = await ball.northboundShareholdingSz();
        console.log('✅ northboundShareholdingSz 成功');
    } catch (error) {
        console.error('❌ northboundShareholdingSz 失败:', error.message);
    }

    // 测试 tranCode
    try {
        const result = ball.tranCode('70001');
        console.log('✅ tranCode 成功:', result);
    } catch (error) {
        console.error('❌ tranCode 失败:', error.message);
    }
}

async function testToken() {
    console.log('\n🔑 测试 Token 模块');

    // 测试 hasToken
    try {
        const result = ball.hasToken();
        console.log('✅ hasToken 成功:', result);
    } catch (error) {
        console.error('❌ hasToken 失败:', error.message);
    }

    // 测试 setToken 和 getToken
    try {
        const testToken = 'test_token=123;u=456';
        ball.setToken(testToken);
        console.log('✅ setToken 成功');

        const retrievedToken = ball.getToken();
        console.log('✅ getToken 成功');

        // 清除测试token
        ball.clearToken();
        console.log('✅ clearToken 成功');
    } catch (error) {
        console.error('❌ Token 操作失败:', error.message);
    }
}

async function testUtils() {
    console.log('\n🔧 测试 Utils 模块');

    // 测试 getCurrentTimestamp
    try {
        const result = ball.getCurrentTimestamp();
        console.log('✅ getCurrentTimestamp 成功:', result);
    } catch (error) {
        console.error('❌ getCurrentTimestamp 失败:', error.message);
    }

    // 测试 formatDate
    try {
        const result = ball.formatDate(new Date());
        console.log('✅ formatDate 成功:', result);
    } catch (error) {
        console.error('❌ formatDate 失败:', error.message);
    }

    // 测试 formatDateSlash
    try {
        const result = ball.formatDateSlash(new Date());
        console.log('✅ formatDateSlash 成功:', result);
    } catch (error) {
        console.error('❌ formatDateSlash 失败:', error.message);
    }
}

// 主测试函数
async function runAllTests() {
    console.log('🚀 开始测试 jsnowball 所有模块...\n');

    try {
        // 设置token（如果需要）
        if (ball.hasToken && !ball.hasToken()) {
            console.log('⚠️  未设置token，某些测试可能失败');
        }

        // 运行所有测试
        await testSuggestStock();
        await testFinance();
        await testRealtime();
        await testCapital();
        await testF10();
        await testReport();
        await testCube();
        await testFund();
        await testBond();
        await testIndexData();
        await testUser();
        await testHkex();

        console.log('\n🎉 所有测试完成！');
        console.log('\n📝 说明：');
        console.log('- ✅ 表示API调用成功（不代表数据正确性）');
        console.log('- ❌ 表示API调用失败');
        console.log('- 某些API需要有效的雪球token才能正常工作');
        console.log('- 测试用的代码和参数可能需要根据实际情况调整');

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
    }
}

// 运行测试
runAllTests();
