/**
 * 简单测试脚本
 * 测试不需要token的功能
 */

import * as ball from './index.js';

async function testBasicFunctions() {
    console.log('🚀 开始测试 jsnowball...\n');
    
    try {
        const searchResult = await ball.suggestStock('平安银行');
        console.log(searchResult);
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error);
    }
}

// 运行测试
testBasicFunctions();
