import { fetch } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 获取用户自选股列表
 * @returns {Promise<Object>} 自选股列表数据
 */
export async function watchList() {
    const url = apiRef.WATCH_LIST;
    return await fetch(url);
}

/**
 * 获取指定自选股组合的股票列表
 * @param {string} pid - 自选股组合ID
 * @returns {Promise<Object>} 自选股股票列表数据
 */
export async function watchStock(pid) {
    if (!pid) {
        throw new Error('Portfolio ID (pid) is required');
    }
    
    const url = apiRef.WATCH_STOCK + pid;
    return await fetch(url);
}

/**
 * 获取所有自选股组合及其股票
 * @returns {Promise<Object>} 所有自选股数据
 */
export async function allWatchStocks() {
    const watchListData = await watchList();
    
    if (!watchListData || !watchListData.data || !watchListData.data.length) {
        return {
            portfolios: [],
            stocks: {}
        };
    }
    
    const portfolios = watchListData.data;
    const stockPromises = portfolios.map(async (portfolio) => {
        try {
            const stocks = await watchStock(portfolio.id);
            return {
                portfolioId: portfolio.id,
                portfolioName: portfolio.name,
                stocks: stocks
            };
        } catch (error) {
            console.warn(`Failed to fetch stocks for portfolio ${portfolio.id}:`, error.message);
            return {
                portfolioId: portfolio.id,
                portfolioName: portfolio.name,
                stocks: null
            };
        }
    });
    
    const stocksData = await Promise.all(stockPromises);
    
    // 组织数据结构
    const result = {
        portfolios: portfolios,
        stocks: {}
    };
    
    stocksData.forEach(item => {
        result.stocks[item.portfolioId] = item.stocks;
    });
    
    return result;
}

/**
 * 获取默认自选股组合的股票
 * @returns {Promise<Object>} 默认自选股组合的股票数据
 */
export async function defaultWatchStocks() {
    const watchListData = await watchList();
    
    if (!watchListData || !watchListData.data || !watchListData.data.length) {
        return null;
    }
    
    // 通常第一个是默认组合
    const defaultPortfolio = watchListData.data[0];
    return await watchStock(defaultPortfolio.id);
}

/**
 * 根据组合名称获取自选股
 * @param {string} portfolioName - 组合名称
 * @returns {Promise<Object>} 指定名称组合的股票数据
 */
export async function watchStocksByName(portfolioName) {
    const watchListData = await watchList();
    
    if (!watchListData || !watchListData.data || !watchListData.data.length) {
        return null;
    }
    
    const portfolio = watchListData.data.find(p => p.name === portfolioName);
    
    if (!portfolio) {
        throw new Error(`Portfolio with name "${portfolioName}" not found`);
    }
    
    return await watchStock(portfolio.id);
}

/**
 * 获取自选股概览信息
 * @returns {Promise<Object>} 自选股概览信息
 */
export async function watchStocksOverview() {
    const watchListData = await watchList();
    
    if (!watchListData || !watchListData.data || !watchListData.data.length) {
        return {
            totalPortfolios: 0,
            portfolios: []
        };
    }
    
    const portfolioSummaries = await Promise.all(
        watchListData.data.map(async (portfolio) => {
            try {
                const stocks = await watchStock(portfolio.id);
                const stockCount = stocks && stocks.data ? stocks.data.length : 0;
                
                return {
                    id: portfolio.id,
                    name: portfolio.name,
                    stockCount: stockCount,
                    description: portfolio.description || ''
                };
            } catch (error) {
                return {
                    id: portfolio.id,
                    name: portfolio.name,
                    stockCount: 0,
                    description: portfolio.description || '',
                    error: error.message
                };
            }
        })
    );
    
    return {
        totalPortfolios: watchListData.data.length,
        portfolios: portfolioSummaries
    };
}
