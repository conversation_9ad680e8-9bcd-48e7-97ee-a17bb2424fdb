import { fetch } from './utils.js';
import * as apiRef from './apiRef.js';

/**
 * 获取股东变化数据
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 股东变化数据
 */
export async function skholderchg(symbol) {
    const url = apiRef.F10_SKHOLDERCHG + symbol;
    return await fetch(url);
}

/**
 * 获取股东信息
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 股东信息
 */
export async function skholder(symbol) {
    const url = apiRef.F10_SKHOLDER + symbol;
    return await fetch(url);
}

/**
 * 获取行业信息
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 行业信息
 */
export async function industry(symbol) {
    const url = apiRef.F10_INDUSTRY + symbol;
    return await fetch(url);
}

/**
 * 获取持股信息
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 持股信息
 */
export async function holders(symbol) {
    const url = apiRef.F10_HOLDERS + symbol;
    return await fetch(url);
}

/**
 * 获取分红送股数据
 * @param {string} symbol - 股票代码
 * @param {number} page - 页码，默认1
 * @param {number} size - 每页数据条数，默认10
 * @returns {Promise<Object>} 分红送股数据
 */
export async function bonus(symbol, page = 1, size = 10) {
    let url = apiRef.F10_BONUS + symbol;
    url += '&page=' + page;
    url += '&size=' + size;
    
    return await fetch(url);
}

/**
 * 获取机构持股变化
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 机构持股变化数据
 */
export async function orgHoldingChange(symbol) {
    const url = apiRef.F10_ORG_HOLDING_CHANGE + symbol;
    return await fetch(url);
}

/**
 * 获取行业对比数据
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 行业对比数据
 */
export async function industryCompare(symbol) {
    const url = apiRef.F10_INDUSTRY_COMPARE + symbol;
    return await fetch(url);
}

/**
 * 获取经营分析数据
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 经营分析数据
 */
export async function businessAnalysis(symbol) {
    const url = apiRef.F10_BUSINESS_ANALYSIS + symbol;
    return await fetch(url);
}

/**
 * 获取股本变化数据
 * @param {string} symbol - 股票代码
 * @param {number} count - 返回数据条数，默认5条
 * @returns {Promise<Object>} 股本变化数据
 */
export async function shareschg(symbol, count = 5) {
    let url = apiRef.F10_SHARESCHG + symbol;
    url += '&count=' + count;
    return await fetch(url);
}

/**
 * 获取十大股东数据
 * @param {string} symbol - 股票代码
 * @param {number} circula - 流通股东标识，默认1
 * @returns {Promise<Object>} 十大股东数据
 */
export async function topHolders(symbol, circula = 1) {
    let url = apiRef.F10_TOP_HOLDERS + symbol;
    url += '&circula=' + circula;
    return await fetch(url);
}

/**
 * 获取主要指标数据
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 主要指标数据
 */
export async function mainIndicator(symbol) {
    const url = apiRef.F10_INDICATOR + symbol;
    return await fetch(url);
}

/**
 * 获取完整的F10数据
 * @param {string} symbol - 股票代码
 * @param {Object} options - 可选参数
 * @param {number} options.bonusPage - 分红数据页码
 * @param {number} options.bonusSize - 分红数据每页条数
 * @returns {Promise<Object>} 完整的F10数据
 */
export async function fullF10(symbol, options = {}) {
    const { bonusPage = 1, bonusSize = 10 } = options;
    
    const [
        skholderchgData,
        skholderData,
        industryData,
        holdersData,
        bonusData,
        orgHoldingData,
        industryCompareData,
        businessAnalysisData,
        topHoldersData,
        mainIndicatorData
    ] = await Promise.all([
        skholderchg(symbol),
        skholder(symbol),
        industry(symbol),
        holders(symbol),
        bonus(symbol, bonusPage, bonusSize),
        orgHoldingChange(symbol),
        industryCompare(symbol),
        businessAnalysis(symbol),
        topHolders(symbol),
        mainIndicator(symbol)
    ]);
    
    return {
        skholderchg: skholderchgData,
        skholder: skholderData,
        industry: industryData,
        holders: holdersData,
        bonus: bonusData,
        orgHolding: orgHoldingData,
        industryCompare: industryCompareData,
        businessAnalysis: businessAnalysisData,
        topHolders: topHoldersData,
        mainIndicator: mainIndicatorData
    };
}

/**
 * 获取股东相关数据
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 股东相关数据
 */
export async function shareholderInfo(symbol) {
    const [skholderchgData, skholderData, holdersData, topHoldersData] = await Promise.all([
        skholderchg(symbol),
        skholder(symbol),
        holders(symbol),
        topHolders(symbol)
    ]);
    
    return {
        changes: skholderchgData,
        shareholders: skholderData,
        holders: holdersData,
        topHolders: topHoldersData
    };
}

/**
 * 获取行业相关数据
 * @param {string} symbol - 股票代码
 * @returns {Promise<Object>} 行业相关数据
 */
export async function industryInfo(symbol) {
    const [industryData, industryCompareData] = await Promise.all([
        industry(symbol),
        industryCompare(symbol)
    ]);
    
    return {
        industry: industryData,
        compare: industryCompareData
    };
}
