# jsnowball

> 雪球APP JavaScript API (需要自取token)

JavaScript version of pysnowball - Xueqiu (雪球) API client using ESM modules and axios.

## 快速指引

安装依赖

```bash
npm install
```

示例

```javascript
import * as ball from './index.js';

// 设置token
ball.setToken("xq_a_token=662745a236*****;u=909119****");

// 获取现金流数据
const cashFlow = await ball.cashFlow('SH600000');
console.log(cashFlow);

// 获取实时行情
const quote = await ball.quotec('SZ002027');
console.log(quote);
```

调用API前需要手动获取雪球网站的token，使用setToken设置token后才能访问雪球的API。

传送门 === [如何获取雪球token](https://blog.crackcreed.com/diy-xue-qiu-app-shu-ju-api/)

## APIs

### 实时行情

获取某支股票的行情数据

```javascript
import { quotec } from './index.js';
const result = await quotec('SZ002027');
```

### 实时分笔

获取实时分笔数据，可以实时取得股票当前报价和成交信息

```javascript
import { pankou } from './index.js';
const result = await pankou('SZ002027');
```

### K线数据

获取K线数据

```javascript
import { kline } from './index.js';
const result = await kline('SH600000', 'day', 100);
```

### 财务数据

获取现金流量表

```javascript
import { cashFlow } from './index.js';
const result = await cashFlow('SH600000');
```

获取资产负债表

```javascript
import { balance } from './index.js';
const result = await balance('SH600000');
```

获取利润表

```javascript
import { income } from './index.js';
const result = await income('SH600000');
```

### 更多功能

- 资本流向数据
- F10数据
- 基金数据
- 组合数据
- 可转债数据
- 指数数据

详细API文档请参考各模块的实现。

## 技术特性

- 使用 ESM 模块系统
- 基于 axios 进行网络请求
- 完全兼容 pysnowball 的 API 接口
- 支持 Node.js 14+

## License

MIT
