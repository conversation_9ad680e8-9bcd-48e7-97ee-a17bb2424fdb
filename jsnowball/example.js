/**
 * jsnowball 使用示例
 * 
 * 运行前请先设置雪球token：
 * 1. 登录雪球网站 https://xueqiu.com
 * 2. 打开浏览器开发者工具
 * 3. 在Network标签页中找到任意请求的Cookie
 * 4. 复制其中的 xq_a_token 和 u 参数
 * 5. 格式如：xq_a_token=xxx;u=xxx
 */

import * as ball from './index.js';

// 示例token（请替换为你自己的token）
const EXAMPLE_TOKEN = "xq_a_token=your_token_here;u=your_user_id_here";

async function runExamples() {
    try {
        // 设置token
        console.log('设置token...');
        ball.setToken(EXAMPLE_TOKEN);
        console.log('Token设置成功');
        
        // 示例1：搜索股票
        console.log('\n=== 示例1：搜索股票 ===');
        const searchResult = await ball.searchStocks('平安银行');
        console.log('搜索结果:', searchResult);
        
        // 示例2：获取实时行情（不需要token）
        console.log('\n=== 示例2：获取实时行情 ===');
        const quote = await ball.quotec('SZ000001');
        console.log('平安银行实时行情:', quote);
        
        // 示例3：获取K线数据
        console.log('\n=== 示例3：获取K线数据 ===');
        const klineData = await ball.kline('SZ000001', 'day', 10);
        console.log('平安银行日K线数据:', klineData);
        
        // 示例4：获取财务数据
        console.log('\n=== 示例4：获取财务数据 ===');
        const cashFlowData = await ball.cashFlow('SZ000001', 0, 5);
        console.log('平安银行现金流数据:', cashFlowData);
        
        // 示例5：获取可转债数据
        console.log('\n=== 示例5：获取可转债数据 ===');
        const bondData = await ball.convertibleBond(10, 1);
        console.log('可转债数据:', bondData);
        
        // 示例6：获取基金信息
        console.log('\n=== 示例6：获取基金信息 ===');
        const fundData = await ball.fundInfo('000001');
        console.log('基金信息:', fundData);
        
    } catch (error) {
        console.error('示例运行出错:', error.message);
        
        if (error.message.includes('未设置TOKEN')) {
            console.log('\n请先设置有效的雪球token！');
            console.log('获取token方法：');
            console.log('1. 登录雪球网站 https://xueqiu.com');
            console.log('2. 打开浏览器开发者工具');
            console.log('3. 在Network标签页中找到任意请求的Cookie');
            console.log('4. 复制其中的 xq_a_token 和 u 参数');
            console.log('5. 格式如：xq_a_token=xxx;u=xxx');
        }
    }
}

// 运行示例
if (import.meta.url === `file://${process.argv[1]}`) {
    runExamples();
}

export { runExamples };
