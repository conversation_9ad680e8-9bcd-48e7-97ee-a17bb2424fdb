import axios from 'axios';
import { getToken } from './token.js';

/**
 * 发送HTTP请求到雪球API
 * @param {string} url - 请求URL
 * @param {string} host - 主机名，默认为 'stock.xueqiu.com'
 * @returns {Promise<Object>} API响应数据
 */
export async function fetch(url, host = 'stock.xueqiu.com') {
    const headers = {
        'Host': host,
        'Accept': 'application/json',
        'Cookie': getToken(),
        'User-Agent': 'Xueqiu iPhone 14.15.1',
        'Accept-Language': 'zh-Hans-CN;q=1, ja-JP;q=0.9',
        'Accept-Encoding': 'br, gzip, deflate',
        'Connection': 'keep-alive'
    };

    try {
        const response = await axios.get(url, { headers });
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`Request failed with status ${error.response.status}: ${error.response.data}`);
        } else {
            throw new Error(`Request failed: ${error.message}`);
        }
    }
}

/**
 * 发送HTTP请求到雪球API（不需要token）
 * @param {string} url - 请求URL
 * @param {string} host - 主机名，默认为 'stock.xueqiu.com'
 * @returns {Promise<Object>} API响应数据
 */
export async function fetchWithoutToken(url, host = 'stock.xueqiu.com') {
    const headers = {
        'Host': host,
        'Accept': 'application/json',
        'User-Agent': 'Xueqiu iPhone 11.8',
        'Accept-Language': 'zh-Hans-CN;q=1, ja-JP;q=0.9',
        'Accept-Encoding': 'br, gzip, deflate',
        'Connection': 'keep-alive'
    };

    try {
        const response = await axios.get(url, { headers });
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`Request failed with status ${error.response.status}: ${error.response.data}`);
        } else {
            throw new Error(`Request failed: ${error.message}`);
        }
    }
}

/**
 * 发送请求到蛋卷基金API
 * @param {string} url - 请求URL
 * @returns {Promise<Object>} API响应数据
 */
export async function fetchDanjuanFund(url) {
    const headers = {
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive'
    };

    try {
        const response = await axios.get(url, { headers });
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`Request failed with status ${error.response.status}: ${error.response.data}`);
        } else {
            throw new Error(`Request failed: ${error.message}`);
        }
    }
}

/**
 * 发送请求到中证指数API
 * @param {string} url - 请求URL
 * @returns {Promise<Object>} API响应数据
 */
export async function fetchCsindex(url) {
    const headers = {
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive'
    };

    try {
        const response = await axios.get(url, { headers });
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`Request failed with status ${error.response.status}: ${error.response.data}`);
        } else {
            throw new Error(`Request failed: ${error.message}`);
        }
    }
}

/**
 * 构建URL查询参数
 * @param {Object} params - 参数对象
 * @returns {string} 查询字符串
 */
export function buildQueryString(params) {
    const searchParams = new URLSearchParams();
    for (const [key, value] of Object.entries(params)) {
        if (value !== undefined && value !== null) {
            searchParams.append(key, value.toString());
        }
    }
    return searchParams.toString();
}

/**
 * 格式化日期为YYYYMMDD格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
}

/**
 * 获取当前时间戳（毫秒）
 * @returns {number} 时间戳
 */
export function getCurrentTimestamp() {
    return Date.now();
}
