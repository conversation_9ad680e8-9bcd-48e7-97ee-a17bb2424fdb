import axios from 'axios';
import { getToken } from './token.js';

/**
 * 发送HTTP请求到雪球API
 * @param {string} url - 请求URL
 * @param {string} host - 主机名，默认为 'stock.xueqiu.com'
 * @returns {Promise<Object>} API响应数据
 */
export async function fetch(url, host = 'stock.xueqiu.com') {
    const headers = {
        'Host': host,
        'Accept': 'application/json',
        'Cookie': getToken(),
        'User-Agent': 'Xueqiu iPhone 14.15.1',
        'Accept-Language': 'zh-Hans-CN;q=1, ja-JP;q=0.9',
        'Accept-Encoding': 'br, gzip, deflate',
        'Connection': 'keep-alive'
    };

    try {
        const response = await axios.get(url, { headers });
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`Request failed with status ${error.response.status}: ${error.response.data}`);
        } else {
            throw new Error(`Request failed: ${error.message}`);
        }
    }
}

/**
 * 发送HTTP请求到雪球API（不需要token）
 * @param {string} url - 请求URL
 * @param {string} host - 主机名，默认为 'stock.xueqiu.com'
 * @returns {Promise<Object>} API响应数据
 */
export async function fetchWithoutToken(url, host = 'stock.xueqiu.com') {
    const headers = {
        'Host': host,
        'Accept': 'application/json',
        'User-Agent': 'Xueqiu iPhone 11.8',
        'Accept-Language': 'zh-Hans-CN;q=1, ja-JP;q=0.9',
        'Accept-Encoding': 'br, gzip, deflate',
        'Connection': 'keep-alive'
    };

    try {
        const response = await axios.get(url, { headers });
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`Request failed with status ${error.response.status}: ${error.response.data}`);
        } else {
            throw new Error(`Request failed: ${error.message}`);
        }
    }
}

/**
 * 发送请求到东方财富API
 * @param {string} url - 请求URL
 * @returns {Promise<Object>} API响应数据
 */
export async function fetchEastmoney(url) {
    const headers = {
        'Host': 'datacenter-web.eastmoney.com',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,cy;q=0.6'
    };

    try {
        const response = await axios.get(url, { headers });
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`Request failed with status ${error.response.status}: ${error.response.data}`);
        } else {
            throw new Error(`Request failed: ${error.message}`);
        }
    }
}

/**
 * 发送请求到蛋卷基金API
 * @param {string} url - 请求URL
 * @returns {Promise<Object>} API响应数据
 */
export async function fetchDanjuanFund(url) {
    const headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36'
    };

    try {
        const response = await axios.get(url, { headers });
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`Request failed with status ${error.response.status}: ${error.response.data}`);
        } else {
            throw new Error(`Request failed: ${error.message}`);
        }
    }
}

/**
 * 发送请求到中证指数API
 * @param {string} url - 请求URL
 * @returns {Promise<Object>} API响应数据
 */
export async function fetchCsindex(url) {
    const headers = {
        'Host': 'www.csindex.com.cn',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,cy;q=0.6'
    };

    try {
        const response = await axios.get(url, { headers });
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`Request failed with status ${error.response.status}: ${error.response.data}`);
        } else {
            throw new Error(`Request failed: ${error.message}`);
        }
    }
}

/**
 * 格式化日期为YYYYMMDD格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
}

/**
 * 发送请求到港交所API
 * @param {string} url - 请求URL
 * @param {string} txtDate - 日期字符串，格式为 YYYY/MM/DD，默认为今天
 * @returns {Promise<string>} API响应的HTML内容
 */
export async function fetchHkc(url, txtDate = null) {
    const today = new Date();
    if (txtDate === null) {
        txtDate = formatDateSlash(today);
    }
    const todayStr = formatDate(today);

    const payload = new URLSearchParams({
        '__VIEWSTATE': '/wEPDwUJNjIxMTYzMDAwZGSFj8kdzCLeVLiJkFRvN5rjsPotqw==',
        '__VIEWSTATEGENERATOR': '3C67932C',
        '__EVENTVALIDATION': '/wEdAAdbi0fj+ZSDYaSP61MAVoEdVobCVrNyCM2j+bEk3ygqmn1KZjrCXCJtWs9HrcHg6Q64ro36uTSn/Z2SUlkm9HsG7WOv0RDD9teZWjlyl84iRMtpPncyBi1FXkZsaSW6dwqO1N1XNFmfsMXJasjxX85ju3P1WAPUeweM/r0/uwwyYLgN1B8=',
        'today': todayStr,
        'sortBy': 'stockcode',
        'sortDirection': 'asc',
        'alertMsg': '',
        'txtShareholdingDate': txtDate,
        'btnSearch': 'Search'
    });

    const headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    };

    try {
        const response = await axios.post(url, payload, {
            headers,
            httpsAgent: new (await import('https')).Agent({
                rejectUnauthorized: false
            })
        });
        return response.data;
    } catch (error) {
        if (error.response) {
            throw new Error(`Request failed with status ${error.response.status}: ${error.response.data}`);
        } else {
            throw new Error(`Request failed: ${error.message}`);
        }
    }
}

/**
 * 格式化日期为YYYY/MM/DD格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
export function formatDateSlash(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}/${month}/${day}`;
}

/**
 * 获取当前时间戳（毫秒）
 * @returns {number} 时间戳
 */
export function getCurrentTimestamp() {
    return Date.now();
}
